"""
Professional Market Data Feed
============================

Institutional-grade market data pipeline for Delta Exchange:
- Real-time and historical data fetching
- Multi-timeframe support (3m, 15m, 1h)
- Professional error handling and retry logic
- Data quality validation
- Event-driven architecture
"""

import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import time
import logging
from dataclasses import dataclass
import hashlib
import hmac
import json

import sys
from pathlib import Path

# Add root directory to path for core imports
root_dir = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(root_dir))

from core.config_manager import ConfigManager
from core.logger import get_logger

@dataclass
class MarketData:
    """Professional market data structure"""
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float
    symbol: str
    timeframe: str

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'timestamp': self.timestamp,
            'open': self.open,
            'high': self.high,
            'low': self.low,
            'close': self.close,
            'volume': self.volume,
            'symbol': self.symbol,
            'timeframe': self.timeframe
        }

class DeltaExchangeClient:
    """Professional Delta Exchange API client"""

    BASE_URL = "https://api.india.delta.exchange"  # Fixed: Use India endpoint

    def __init__(self, api_key: str, secret_key: str):
        """Initialize Delta Exchange client

        Args:
            api_key: Delta Exchange API key
            secret_key: Delta Exchange secret key
        """
        self.api_key = api_key
        self.secret_key = secret_key
        self.session = requests.Session()
        self.logger = get_logger()

        # Set headers
        self.session.headers.update({
            'User-Agent': 'Professional-Trading-System/1.0',
            'Content-Type': 'application/json'
        })

        self.logger.info("[OK] Delta Exchange client initialized")

    def _generate_signature(self, method: str, endpoint: str, payload: str = "") -> str:
        """Generate API signature for authentication

        Args:
            method: HTTP method
            endpoint: API endpoint
            payload: Request payload

        Returns:
            HMAC signature
        """
        timestamp = str(int(time.time()))
        message = method + timestamp + endpoint + payload

        signature = hmac.new(
            self.secret_key.encode(),
            message.encode(),
            hashlib.sha256
        ).hexdigest()

        return signature, timestamp

    def _make_request(self, method: str, endpoint: str, params: Optional[Dict] = None,
                     data: Optional[Dict] = None, authenticated: bool = False) -> Dict:
        """Make API request with professional error handling

        Args:
            method: HTTP method
            endpoint: API endpoint
            params: Query parameters
            data: Request data
            authenticated: Whether to authenticate request

        Returns:
            API response
        """
        url = f"{self.BASE_URL}{endpoint}"

        headers = {}
        if authenticated:
            payload = json.dumps(data) if data else ""
            signature, timestamp = self._generate_signature(method, endpoint, payload)

            headers.update({
                'api-key': self.api_key,
                'signature': signature,
                'timestamp': timestamp
            })

        try:
            response = self.session.request(
                method=method,
                url=url,
                params=params,
                json=data,
                headers=headers,
                timeout=30
            )

            response.raise_for_status()
            return response.json()

        except requests.exceptions.RequestException as e:
            self.logger.error(f"[ERROR] API request failed: {e}")
            raise
        except json.JSONDecodeError as e:
            self.logger.error(f"[ERROR] Invalid JSON response: {e}")
            raise

    def get_products(self) -> List[Dict]:
        """Get available trading products

        Returns:
            List of trading products
        """
        try:
            response = self._make_request('GET', '/v2/products')
            return response.get('result', [])
        except Exception as e:
            self.logger.error(f"[ERROR] Failed to get products: {e}")
            return []

    def get_btc_product_id(self) -> Optional[int]:
        """Get BTC-USD product ID

        Returns:
            BTC product ID or None if not found
        """
        products = self.get_products()

        # First try to find BTCUSD perpetual futures
        for product in products:
            if (product.get('symbol') == 'BTCUSD' and
                product.get('product_type') == 'perpetual_futures'):
                self.logger.info(f"[OK] Found BTCUSD perpetual: ID {product.get('id')}")
                return product.get('id')

        # Fallback: try any BTCUSD product
        for product in products:
            if product.get('symbol') == 'BTCUSD':
                self.logger.info(f"[OK] Found BTCUSD product: ID {product.get('id')}, type: {product.get('product_type')}")
                return product.get('id')

        self.logger.warning("[WARNING] BTC-USD product not found")
        return None

class MarketDataFeed:
    """Professional market data feed manager"""

    TIMEFRAME_MAPPING = {
        '1m': '1m',
        '3m': '3m',
        '5m': '5m',
        '15m': '15m',
        '30m': '30m',
        '1h': '1h',
        '4h': '4h',
        '1d': '1d'
    }

    def __init__(self, config_manager: ConfigManager):
        """Initialize market data feed

        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager
        self.logger = get_logger()

        # Initialize Delta client
        credentials = self.config.get_delta_credentials()
        self.delta_client = DeltaExchangeClient(
            credentials['api_key'],
            credentials['secret_key']
        )

        # Get BTC product ID
        self.btc_product_id = self.delta_client.get_btc_product_id()
        if not self.btc_product_id:
            raise ValueError("[ERROR] Could not find BTC product on Delta Exchange")

        self.logger.info(f"[OK] Market data feed initialized - BTC Product ID: {self.btc_product_id}")

    def fetch_historical_data(self,
                            timeframe: str = '3m',
                            days: int = 30,
                            end_time: Optional[datetime] = None) -> pd.DataFrame:
        """Fetch historical OHLCV data with chunked requests for large date ranges

        Args:
            timeframe: Timeframe (3m, 15m, 1h, etc.)
            days: Number of days to fetch
            end_time: End time (default: now)

        Returns:
            DataFrame with OHLCV data
        """
        try:
            if end_time is None:
                end_time = datetime.utcnow()

            # Calculate maximum days per chunk based on Delta Exchange 4000 candle limit
            max_days_per_chunk = self._calculate_max_days_per_chunk(timeframe)

            self.logger.info(f"[CHUNKED_DEBUG] Timeframe: {timeframe}, Days requested: {days}, Max per chunk: {max_days_per_chunk}")

            if days <= max_days_per_chunk:
                # Single request for small date ranges
                self.logger.info(f"[CHUNKED_DEBUG] Using single request for {days} days")
                return self._fetch_single_chunk(timeframe, days, end_time)
            else:
                # Multiple requests for large date ranges
                self.logger.info(f"[CHUNKED_DEBUG] Using chunked requests for {days} days")
                return self._fetch_chunked_data(timeframe, days, end_time, max_days_per_chunk)

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to fetch historical data: {e}")
            import traceback
            self.logger.error(f"[ERROR] Traceback: {traceback.format_exc()}")
            return pd.DataFrame()

    def _calculate_max_days_per_chunk(self, timeframe: str) -> int:
        """Calculate maximum days per chunk based on 4000 candle API limit

        Args:
            timeframe: Timeframe string

        Returns:
            Maximum days that can be fetched in one request
        """
        # Candles per day for each timeframe
        candles_per_day = {
            '3m': 480,   # 24 hours * 60 minutes / 3 minutes
            '15m': 96,   # 24 hours * 60 minutes / 15 minutes
            '1h': 24,    # 24 hours / 1 hour
            '4h': 6,     # 24 hours / 4 hours
            '1d': 1      # 1 candle per day
        }

        candles_per_day_for_tf = candles_per_day.get(timeframe, 96)  # Default to 15m

        # Use 3500 instead of 4000 to leave buffer for API variations
        max_days = int(3500 / candles_per_day_for_tf)

        # Ensure minimum of 1 day
        return max(1, max_days)

    def _fetch_single_chunk(self, timeframe: str, days: int, end_time: datetime) -> pd.DataFrame:
        """Fetch data for a single chunk (small date range)

        Args:
            timeframe: Timeframe string
            days: Number of days to fetch
            end_time: End time

        Returns:
            DataFrame with OHLCV data
        """
        start_time = end_time - timedelta(days=days)

        # Convert to timestamps
        start_ts = int(start_time.timestamp())
        end_ts = int(end_time.timestamp())

        # Fetch data from Delta Exchange using symbol (not product_id)
        params = {
            'resolution': self.TIMEFRAME_MAPPING.get(timeframe, timeframe),
            'start': start_ts,
            'end': end_ts,
            'symbol': 'BTCUSD'
        }

        self.logger.info(f"[DATA] Fetching data with params: {params}")

        response = self.delta_client._make_request(
            'GET',
            '/v2/history/candles',
            params=params
        )

        # Process response
        self.logger.info(f"[DATA] API Response keys: {list(response.keys())}")

        if not response.get('success', False):
            self.logger.error(f"[ERROR] API returned error: {response}")
            return pd.DataFrame()

        candles = response.get('result', [])
        if not candles:
            self.logger.warning(f"[WARNING] No data received for {timeframe}. Response: {response}")
            return pd.DataFrame()

        self.logger.info(f"[DATA] Received {len(candles)} raw candles")

        # Convert to DataFrame
        df = self._process_candle_data(candles, timeframe)

        self.logger.info(f"[OK] Fetched {len(df)} {timeframe} candles for BTC")
        return df

    def _fetch_chunked_data(self, timeframe: str, days: int, end_time: datetime, max_days_per_chunk: int) -> pd.DataFrame:
        """Fetch data in chunks for large date ranges

        Args:
            timeframe: Timeframe string
            days: Total number of days to fetch
            end_time: End time
            max_days_per_chunk: Maximum days per chunk

        Returns:
            Combined DataFrame with all chunks
        """
        all_chunks = []
        current_end_time = end_time
        remaining_days = days
        chunk_number = 1

        # Calculate total chunks for progress tracking
        total_chunks = (days + max_days_per_chunk - 1) // max_days_per_chunk

        self.logger.info(f"[CHUNKED] Fetching {days} days of {timeframe} data in {total_chunks} chunks")
        self.logger.info(f"[CHUNKED] Max days per chunk: {max_days_per_chunk}")

        while remaining_days > 0:
            # Calculate days for this chunk
            chunk_days = min(remaining_days, max_days_per_chunk)

            self.logger.info(f"[CHUNKED] Fetching chunk {chunk_number}/{total_chunks}: {chunk_days} days ending at {current_end_time}")

            # Fetch this chunk
            chunk_df = self._fetch_single_chunk(timeframe, chunk_days, current_end_time)

            if not chunk_df.empty:
                all_chunks.append(chunk_df)
                self.logger.info(f"[CHUNKED] Chunk {chunk_number} successful: {len(chunk_df)} candles")
            else:
                self.logger.warning(f"[CHUNKED] Chunk {chunk_number} failed or empty")

            # Move to next chunk
            current_end_time = current_end_time - timedelta(days=chunk_days)
            remaining_days -= chunk_days
            chunk_number += 1

            # Rate limiting between chunks
            if remaining_days > 0:
                import time
                time.sleep(0.5)  # 500ms delay between chunks

        # Combine all chunks
        if not all_chunks:
            self.logger.warning(f"[CHUNKED] No data fetched for {timeframe}")
            return pd.DataFrame()

        # Concatenate all chunks
        combined_df = pd.concat(all_chunks, ignore_index=False)

        # Remove duplicates and sort
        combined_df = combined_df.drop_duplicates()
        combined_df = combined_df.sort_index()

        self.logger.info(f"[CHUNKED] Combined {len(all_chunks)} chunks into {len(combined_df)} total candles")
        self.logger.info(f"[CHUNKED] Date range: {combined_df.index.min()} to {combined_df.index.max()}")

        return combined_df

    def _process_candle_data(self, candles: List[Dict], timeframe: str) -> pd.DataFrame:
        """Process raw candle data into DataFrame

        Args:
            candles: Raw candle data from API
            timeframe: Timeframe string

        Returns:
            Processed DataFrame
        """
        try:
            data = []

            # Debug: log first few candles to understand format
            if candles:
                self.logger.info(f"[DEBUG] Sample candle data: {candles[0]}")
                self.logger.info(f"[DEBUG] Candle length: {len(candles[0])}")

            for candle in candles:
                try:
                    # Delta Exchange candle format: dictionary with keys: time, open, high, low, close, volume
                    if isinstance(candle, dict) and all(key in candle for key in ['time', 'open', 'high', 'low', 'close', 'volume']):
                        # Handle potential None or invalid values
                        timestamp = candle.get('time', 0)
                        open_price = float(candle.get('open', 0)) if candle.get('open') is not None else 0.0
                        high_price = float(candle.get('high', 0)) if candle.get('high') is not None else 0.0
                        low_price = float(candle.get('low', 0)) if candle.get('low') is not None else 0.0
                        close_price = float(candle.get('close', 0)) if candle.get('close') is not None else 0.0
                        volume = float(candle.get('volume', 0)) if candle.get('volume') is not None else 0.0

                        # Skip invalid candles
                        if timestamp == 0 or close_price == 0:
                            continue

                        data.append({
                            'timestamp': pd.to_datetime(timestamp, unit='s'),
                            'open': open_price,
                            'high': high_price,
                            'low': low_price,
                            'close': close_price,
                            'volume': volume,
                            'timeframe': timeframe
                        })
                    else:
                        self.logger.warning(f"[WARNING] Unexpected candle format: {candle}")

                except (ValueError, TypeError, KeyError) as e:
                    self.logger.warning(f"[WARNING] Skipping invalid candle: {candle}, error: {e}")
                    continue

            if not data:
                return pd.DataFrame()

            df = pd.DataFrame(data)
            df.set_index('timestamp', inplace=True)
            df.sort_index(inplace=True)

            # Data validation
            df = self._validate_data(df)

            return df

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to process candle data: {e}")
            return pd.DataFrame()

    def _validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Validate and clean market data

        Args:
            df: Raw DataFrame

        Returns:
            Validated DataFrame
        """
        if df.empty:
            return df

        original_len = len(df)

        # Remove invalid data
        df = df.dropna()
        df = df[df['high'] >= df['low']]
        df = df[df['high'] >= df['open']]
        df = df[df['high'] >= df['close']]
        df = df[df['low'] <= df['open']]
        df = df[df['low'] <= df['close']]
        df = df[df['volume'] >= 0]

        # Remove extreme outliers (price changes > 50%)
        df['price_change'] = df['close'].pct_change().abs()
        df = df[df['price_change'] <= 0.5]
        df.drop('price_change', axis=1, inplace=True)

        cleaned_len = len(df)
        if cleaned_len < original_len:
            self.logger.warning(f"[WARNING] Removed {original_len - cleaned_len} invalid candles")

        return df

    def get_latest_price(self) -> Optional[float]:
        """Get latest BTC price

        Returns:
            Latest price or None if failed
        """
        try:
            response = self.delta_client._make_request(
                'GET',
                f'/v2/products/{self.btc_product_id}/ticker'
            )

            ticker = response.get('result', {})
            return float(ticker.get('close', 0))

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to get latest price: {e}")
            return None

    def is_market_open(self) -> bool:
        """Check if market is open (crypto markets are always open)

        Returns:
            True (crypto markets are 24/7)
        """
        return True
