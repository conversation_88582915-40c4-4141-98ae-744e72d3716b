"""
Test script to verify optimizations work correctly
"""

import sys
import os
import time
import logging

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_duckduckgo_news():
    """Test DuckDuckGo news integration"""
    print("🧪 Testing DuckDuckGo News Integration...")
    
    try:
        from helpers.duckduckgo_news import duckduckgo_news
        
        # Test market news
        print("📰 Testing market news...")
        start_time = time.time()
        news_result = duckduckgo_news.get_market_news("^NSEI", max_results=5)
        end_time = time.time()
        
        print(f"⏱️ News fetch time: {end_time - start_time:.2f}s")
        print(f"✅ Success: {news_result.get('success', False)}")
        print(f"📊 Articles found: {news_result.get('news_count', 0)}")
        
        if news_result.get('success') and news_result.get('news_articles'):
            print("📄 Sample article:")
            article = news_result['news_articles'][0]
            print(f"   Title: {article.get('title', 'N/A')[:80]}...")
            print(f"   URL: {article.get('url', 'N/A')}")
            print(f"   Source: {article.get('source', 'N/A')}")
        
        # Test FII/DII news
        print("\n💰 Testing FII/DII news...")
        start_time = time.time()
        fii_result = duckduckgo_news.get_fii_dii_news()
        end_time = time.time()
        
        print(f"⏱️ FII/DII fetch time: {end_time - start_time:.2f}s")
        print(f"✅ Success: {fii_result.get('success', False)}")
        print(f"📊 Articles found: {fii_result.get('news_count', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ DuckDuckGo news test failed: {str(e)}")
        return False

def test_parallel_execution():
    """Test parallel tool execution"""
    print("\n🧪 Testing Parallel Tool Execution...")
    
    try:
        from helpers.parallel_executor import trading_executor
        
        # Test Indian market analysis
        print("🇮🇳 Testing Indian market parallel execution...")
        start_time = time.time()
        
        def progress_callback(message, current, total):
            print(f"   Progress ({current}/{total}): {message}")
        
        results = trading_executor.execute_indian_market_analysis("^NSEI", progress_callback)
        end_time = time.time()
        
        print(f"⏱️ Parallel execution time: {end_time - start_time:.2f}s")
        print(f"🔧 Tools executed: {len(results)}")
        
        for tool_name, result in results.items():
            print(f"   {tool_name}: {'✅' if result.success else '❌'} ({result.execution_time:.2f}s)")
        
        return True
        
    except Exception as e:
        print(f"❌ Parallel execution test failed: {str(e)}")
        return False

def test_json_prompt_format():
    """Test that prompts enforce JSON format"""
    print("\n🧪 Testing JSON Prompt Format...")
    
    try:
        from helpers.prompts import positional_prompt, scalp_prompt, crypto_prompt
        
        # Check that prompts contain JSON requirements
        prompts_to_check = [
            ("Positional", positional_prompt),
            ("Scalp", scalp_prompt), 
            ("Crypto", crypto_prompt)
        ]
        
        for prompt_name, prompt_text in prompts_to_check:
            print(f"📋 Checking {prompt_name} prompt...")
            
            # Check for JSON format requirements
            has_json_requirement = "JSON" in prompt_text and "MANDATORY" in prompt_text
            has_exact_format = "EXACTLY this JSON structure" in prompt_text
            has_no_exceptions = "NO EXCEPTIONS" in prompt_text
            
            print(f"   JSON requirement: {'✅' if has_json_requirement else '❌'}")
            print(f"   Exact format spec: {'✅' if has_exact_format else '❌'}")
            print(f"   No exceptions clause: {'✅' if has_no_exceptions else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ JSON prompt format test failed: {str(e)}")
        return False

def test_optimized_utils():
    """Test optimized utils can be imported and initialized"""
    print("\n🧪 Testing Optimized Utils...")
    
    try:
        from helpers.optimized_utils import generate_analysis_optimized, _create_tool_summary, _parse_analysis_response
        
        print("✅ Optimized utils imported successfully")
        
        # Test tool summary creation
        mock_tool_results = {
            "get_market_news": type('MockResult', (), {
                'success': True,
                'data': {
                    'success': True,
                    'news_articles': [
                        {'title': 'Test News', 'source': 'Test Source', 'url': 'https://test.com'}
                    ]
                }
            })()
        }
        
        summary = _create_tool_summary(mock_tool_results)
        print(f"✅ Tool summary created: {len(summary)} characters")
        
        # Test JSON parsing
        test_json = '{"status": "test", "analysis_summary": "test summary"}'
        parsed = _parse_analysis_response(test_json)
        print(f"✅ JSON parsing works: {parsed.get('status') == 'test'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Optimized utils test failed: {str(e)}")
        return False

def main():
    """Run all optimization tests"""
    print("🚀 Starting Optimization Tests...\n")
    
    tests = [
        ("DuckDuckGo News", test_duckduckgo_news),
        ("Parallel Execution", test_parallel_execution),
        ("JSON Prompt Format", test_json_prompt_format),
        ("Optimized Utils", test_optimized_utils)
    ]
    
    results = []
    total_start_time = time.time()
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🧪 Running {test_name} Test")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {str(e)}")
            results.append((test_name, False))
    
    total_time = time.time() - total_start_time
    
    # Summary
    print(f"\n{'='*50}")
    print("📊 TEST SUMMARY")
    print('='*50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    print(f"⏱️ Total time: {total_time:.2f}s")
    
    if passed == total:
        print("🎉 All optimizations working correctly!")
        return True
    else:
        print("⚠️ Some optimizations need attention")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
