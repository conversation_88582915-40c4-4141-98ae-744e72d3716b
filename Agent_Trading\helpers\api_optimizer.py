"""
API Usage Optimizer
Intelligent API call management to reduce costs and improve performance
"""

import json
import time
import hashlib
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class CacheEntry:
    """Cache entry with TTL and metadata"""
    data: Any
    timestamp: datetime
    ttl_seconds: int
    request_hash: str
    api_calls_saved: int = 0

class APIOptimizer:
    """Optimizes API usage through intelligent caching and request batching"""
    
    def __init__(self, cache_file: str = "api_cache.json"):
        self.cache_file = cache_file
        self.cache: Dict[str, CacheEntry] = {}
        self.api_stats = {
            'total_requests': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'api_calls_saved': 0,
            'cost_saved_estimate': 0.0
        }
        self.load_cache()
        
        # Cost estimates (per 1K tokens)
        self.cost_estimates = {
            'gemini-2.5-flash': 0.00015,  # $0.00015 per 1K tokens
            'gemini-2.0-flash': 0.00010,  # $0.00010 per 1K tokens
            'gemini-pro': 0.0005,         # $0.0005 per 1K tokens
        }
        
        # Cache TTL settings based on content type
        self.ttl_settings = {
            'market_data': 300,      # 5 minutes for market data
            'news': 600,             # 10 minutes for news
            'chart_analysis': 900,   # 15 minutes for chart analysis
            'technical_analysis': 1800,  # 30 minutes for technical analysis
            'general_query': 3600,   # 1 hour for general queries
        }
    
    def should_use_cache(self, request_type: str, content: str) -> bool:
        """Determine if request should use caching"""
        
        # Always cache these types
        cacheable_types = [
            'market_data', 'news', 'technical_analysis', 
            'chart_analysis', 'general_query'
        ]
        
        if request_type in cacheable_types:
            return True
        
        # Cache based on content characteristics
        if len(content) > 1000:  # Large content likely to be expensive
            return True
        
        # Don't cache real-time or personalized requests
        no_cache_keywords = ['real-time', 'current', 'now', 'latest', 'live']
        content_lower = content.lower()
        
        return not any(keyword in content_lower for keyword in no_cache_keywords)
    
    def get_cache_key(self, request_type: str, content: str, model: str = "gemini-2.5-flash") -> str:
        """Generate cache key for request"""
        # Create hash from request components
        key_data = f"{request_type}:{model}:{content}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def get_cached_response(self, cache_key: str) -> Optional[Any]:
        """Get cached response if valid"""
        if cache_key not in self.cache:
            return None
        
        entry = self.cache[cache_key]
        
        # Check if cache entry is still valid
        if datetime.now() - entry.timestamp > timedelta(seconds=entry.ttl_seconds):
            # Cache expired, remove it
            del self.cache[cache_key]
            self.save_cache()
            return None
        
        # Cache hit
        self.api_stats['cache_hits'] += 1
        self.api_stats['api_calls_saved'] += 1
        
        logger.info(f"Cache hit for key: {cache_key[:8]}...")
        return entry.data
    
    def cache_response(self, cache_key: str, response: Any, request_type: str, 
                      estimated_tokens: int = 1000) -> None:
        """Cache API response"""
        
        ttl = self.ttl_settings.get(request_type, 3600)
        
        entry = CacheEntry(
            data=response,
            timestamp=datetime.now(),
            ttl_seconds=ttl,
            request_hash=cache_key
        )
        
        self.cache[cache_key] = entry
        self.api_stats['cache_misses'] += 1
        
        # Estimate cost saved for future cache hits
        model_cost = self.cost_estimates.get('gemini-2.5-flash', 0.00015)
        cost_saved = (estimated_tokens / 1000) * model_cost
        self.api_stats['cost_saved_estimate'] += cost_saved
        
        # Save cache periodically
        if len(self.cache) % 10 == 0:  # Save every 10 entries
            self.save_cache()
        
        logger.info(f"Cached response for key: {cache_key[:8]}... (TTL: {ttl}s)")
    
    def optimize_request(self, request_type: str, content: str, 
                        model: str = "gemini-2.5-flash") -> Dict[str, Any]:
        """Optimize API request parameters"""
        
        optimization = {
            'use_cache': self.should_use_cache(request_type, content),
            'cache_key': self.get_cache_key(request_type, content, model),
            'estimated_tokens': self._estimate_tokens(content),
            'recommended_model': self._recommend_model(request_type, content),
            'thinking_budget': self._recommend_thinking_budget(request_type, content),
            'batch_eligible': self._is_batch_eligible(request_type, content)
        }
        
        return optimization
    
    def _estimate_tokens(self, content: str) -> int:
        """Estimate token count for content"""
        # Rough estimation: 1 token ≈ 4 characters
        return max(100, len(content) // 4)
    
    def _recommend_model(self, request_type: str, content: str) -> str:
        """Recommend optimal model based on request"""
        
        content_length = len(content)
        
        # For simple queries, use Flash
        if request_type in ['market_data', 'news'] and content_length < 1000:
            return 'gemini-2.5-flash'
        
        # For complex analysis, use Flash with thinking
        if request_type in ['chart_analysis', 'technical_analysis']:
            return 'gemini-2.5-flash'
        
        # Default to Flash for cost efficiency
        return 'gemini-2.5-flash'
    
    def _recommend_thinking_budget(self, request_type: str, content: str) -> int:
        """Recommend thinking budget based on complexity"""
        
        thinking_budgets = {
            'market_data': 0,        # No thinking needed for data
            'news': 512,             # Light thinking for news analysis
            'chart_analysis': 4096,  # Medium thinking for charts
            'technical_analysis': 8192,  # Heavy thinking for technical analysis
            'general_query': 1024    # Light thinking for general queries
        }
        
        base_budget = thinking_budgets.get(request_type, 1024)
        
        # Adjust based on content complexity
        content_length = len(content)
        if content_length > 2000:
            base_budget = min(8192, base_budget * 2)
        elif content_length < 500:
            base_budget = max(0, base_budget // 2)
        
        return base_budget
    
    def _is_batch_eligible(self, request_type: str, content: str) -> bool:
        """Check if request can be batched"""
        
        # These types can be batched
        batchable_types = ['market_data', 'news', 'general_query']
        
        return request_type in batchable_types and len(content) < 1000
    
    def batch_requests(self, requests: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Batch multiple requests for efficiency"""
        
        # Group requests by type and model
        batches = {}
        
        for req in requests:
            key = f"{req.get('type', 'general')}:{req.get('model', 'gemini-2.5-flash')}"
            if key not in batches:
                batches[key] = []
            batches[key].append(req)
        
        # Process each batch
        results = []
        for batch_key, batch_requests in batches.items():
            if len(batch_requests) > 1:
                # Combine requests into single prompt
                combined_prompt = self._combine_requests(batch_requests)
                batch_result = {
                    'type': 'batch',
                    'requests': batch_requests,
                    'combined_prompt': combined_prompt,
                    'optimization': 'batched'
                }
                results.append(batch_result)
            else:
                # Single request, process normally
                results.extend(batch_requests)
        
        return results
    
    def _combine_requests(self, requests: List[Dict[str, Any]]) -> str:
        """Combine multiple requests into single prompt"""
        
        combined = "Please analyze the following multiple requests:\n\n"
        
        for i, req in enumerate(requests, 1):
            combined += f"Request {i}: {req.get('content', '')}\n\n"
        
        combined += "Please provide separate responses for each request, clearly labeled."
        
        return combined
    
    def get_optimization_stats(self) -> Dict[str, Any]:
        """Get API optimization statistics"""
        
        total_requests = self.api_stats['cache_hits'] + self.api_stats['cache_misses']
        cache_hit_rate = (self.api_stats['cache_hits'] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'total_requests': total_requests,
            'cache_hits': self.api_stats['cache_hits'],
            'cache_misses': self.api_stats['cache_misses'],
            'cache_hit_rate': f"{cache_hit_rate:.1f}%",
            'api_calls_saved': self.api_stats['api_calls_saved'],
            'estimated_cost_saved': f"${self.api_stats['cost_saved_estimate']:.4f}",
            'cache_entries': len(self.cache),
            'cache_size_mb': self._get_cache_size_mb()
        }
    
    def _get_cache_size_mb(self) -> float:
        """Estimate cache size in MB"""
        try:
            cache_str = json.dumps(self._serialize_cache())
            return len(cache_str.encode()) / (1024 * 1024)
        except:
            return 0.0
    
    def cleanup_expired_cache(self) -> int:
        """Remove expired cache entries"""
        
        expired_keys = []
        current_time = datetime.now()
        
        for key, entry in self.cache.items():
            if current_time - entry.timestamp > timedelta(seconds=entry.ttl_seconds):
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.cache[key]
        
        if expired_keys:
            self.save_cache()
            logger.info(f"Cleaned up {len(expired_keys)} expired cache entries")
        
        return len(expired_keys)
    
    def load_cache(self) -> None:
        """Load cache from file"""
        try:
            with open(self.cache_file, 'r') as f:
                cache_data = json.load(f)
                
            # Deserialize cache entries
            for key, entry_data in cache_data.items():
                self.cache[key] = CacheEntry(
                    data=entry_data['data'],
                    timestamp=datetime.fromisoformat(entry_data['timestamp']),
                    ttl_seconds=entry_data['ttl_seconds'],
                    request_hash=entry_data['request_hash']
                )
                
        except FileNotFoundError:
            logger.info("No cache file found, starting with empty cache")
        except Exception as e:
            logger.error(f"Failed to load cache: {e}")
    
    def save_cache(self) -> None:
        """Save cache to file"""
        try:
            cache_data = self._serialize_cache()
            with open(self.cache_file, 'w') as f:
                json.dump(cache_data, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save cache: {e}")
    
    def _serialize_cache(self) -> Dict[str, Any]:
        """Serialize cache for JSON storage"""
        serialized = {}
        
        for key, entry in self.cache.items():
            serialized[key] = {
                'data': entry.data,
                'timestamp': entry.timestamp.isoformat(),
                'ttl_seconds': entry.ttl_seconds,
                'request_hash': entry.request_hash
            }
        
        return serialized

# Global instance
api_optimizer = APIOptimizer()
