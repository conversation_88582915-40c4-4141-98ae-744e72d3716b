#!/usr/bin/env python3
"""
Quick test to verify the API key fix for generate_analysis_optimized
"""

import sys
import os

# Add the project root to the Python path
script_dir = os.path.dirname(__file__)
project_root = os.path.abspath(os.path.join(script_dir, '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def test_api_key_parameter():
    """Test that the API key parameter is properly added to the optimized function"""
    print("🧪 Testing API key parameter fix...")
    
    try:
        from Agent_Trading.helpers.optimized_utils import generate_analysis_optimized
        import inspect
        
        # Check function signature
        sig = inspect.signature(generate_analysis_optimized)
        params = list(sig.parameters.keys())
        
        print(f"✅ Function parameters: {params}")
        
        # Verify api_key parameter exists
        if 'api_key' in params:
            print("✅ API key parameter found!")
            
            # Check parameter details
            api_key_param = sig.parameters['api_key']
            print(f"   - Type annotation: {api_key_param.annotation}")
            print(f"   - Default value: {api_key_param.default}")
            
            return True
        else:
            print("❌ API key parameter missing!")
            return False
            
    except Exception as e:
        print(f"❌ Error testing API key parameter: {e}")
        return False

def test_backward_compatibility():
    """Test that the backward compatibility function works"""
    print("\n🔄 Testing backward compatibility...")
    
    try:
        from Agent_Trading.helpers.optimized_utils import generate_analysis_cloud_optimized
        import inspect
        
        # Check that the wrapper function exists
        sig = inspect.signature(generate_analysis_cloud_optimized)
        print("✅ Backward compatibility function exists")
        print(f"   - Function signature: {sig}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing backward compatibility: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing API Key Fix for Optimized Analysis\n")
    
    tests = [
        test_api_key_parameter,
        test_backward_compatibility
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! API key fix is working correctly.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
