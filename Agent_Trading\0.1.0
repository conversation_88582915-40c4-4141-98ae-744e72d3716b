Collecting google-genai
  Downloading google_genai-1.28.0-py3-none-any.whl.metadata (43 kB)
Requirement already satisfied: anyio<5.0.0,>=4.8.0 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from google-genai) (4.9.0)
Requirement already satisfied: google-auth<3.0.0,>=2.14.1 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from google-genai) (2.40.3)
Requirement already satisfied: httpx<1.0.0,>=0.28.1 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from google-genai) (0.28.1)
Requirement already satisfied: pydantic<3.0.0,>=2.0.0 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from google-genai) (2.11.7)
Requirement already satisfied: requests<3.0.0,>=2.28.1 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from google-genai) (2.32.4)
Collecting tenacity<9.0.0,>=8.2.3 (from google-genai)
  Using cached tenacity-8.5.0-py3-none-any.whl.metadata (1.2 kB)
Requirement already satisfied: websockets<15.1.0,>=13.0.0 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from google-genai) (15.0.1)
Requirement already satisfied: typing-extensions<5.0.0,>=4.11.0 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from google-genai) (4.14.1)
Requirement already satisfied: idna>=2.8 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from anyio<5.0.0,>=4.8.0->google-genai) (3.10)
Requirement already satisfied: sniffio>=1.1 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from anyio<5.0.0,>=4.8.0->google-genai) (1.3.1)
Requirement already satisfied: cachetools<6.0,>=2.0.0 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from google-auth<3.0.0,>=2.14.1->google-genai) (5.5.2)
Requirement already satisfied: pyasn1-modules>=0.2.1 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from google-auth<3.0.0,>=2.14.1->google-genai) (0.4.2)
Requirement already satisfied: rsa<5,>=3.1.4 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from google-auth<3.0.0,>=2.14.1->google-genai) (4.9.1)
Requirement already satisfied: certifi in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from httpx<1.0.0,>=0.28.1->google-genai) (2025.7.14)
Requirement already satisfied: httpcore==1.* in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from httpx<1.0.0,>=0.28.1->google-genai) (1.0.9)
Requirement already satisfied: h11>=0.16 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from httpcore==1.*->httpx<1.0.0,>=0.28.1->google-genai) (0.16.0)
Requirement already satisfied: annotated-types>=0.6.0 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from pydantic<3.0.0,>=2.0.0->google-genai) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from pydantic<3.0.0,>=2.0.0->google-genai) (2.33.2)
Requirement already satisfied: typing-inspection>=0.4.0 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from pydantic<3.0.0,>=2.0.0->google-genai) (0.4.1)
Requirement already satisfied: charset_normalizer<4,>=2 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from requests<3.0.0,>=2.28.1->google-genai) (3.4.2)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from requests<3.0.0,>=2.28.1->google-genai) (2.4.0)
Requirement already satisfied: pyasn1>=0.1.3 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from rsa<5,>=3.1.4->google-auth<3.0.0,>=2.14.1->google-genai) (0.6.1)
Downloading google_genai-1.28.0-py3-none-any.whl (219 kB)
Using cached tenacity-8.5.0-py3-none-any.whl (28 kB)
Installing collected packages: tenacity, google-genai
  Attempting uninstall: tenacity
    Found existing installation: tenacity 9.1.2
    Uninstalling tenacity-9.1.2:
      Successfully uninstalled tenacity-9.1.2

Successfully installed google-genai-1.28.0 tenacity-8.5.0
