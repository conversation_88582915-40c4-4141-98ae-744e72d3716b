"""
DuckDuckGo News Integration for Fast, Reliable News with Real URLs
Replaces complex RSS parsing with simple, fast search
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import asyncio
import concurrent.futures
try:
    from ddgs import DDGS
except ImportError:
    # Fallback to old package name
    from duckduckgo_search import DDGS

logger = logging.getLogger(__name__)

class DuckDuckGoNewsProvider:
    """Fast news provider using DuckDuckGo search with real URLs"""
    
    def __init__(self):
        self.ddgs = DDGS()
        
    def get_market_news(self, symbol: str, max_results: int = 10) -> Dict[str, Any]:
        """
        Get market news for a specific symbol using DuckDuckGo search
        
        Args:
            symbol: Trading symbol (e.g., "^NSEI", "BTC-USD", "AAPL")
            max_results: Maximum number of news articles to return
            
        Returns:
            Dictionary with news articles and metadata
        """
        try:
            # Create search query based on symbol
            search_query = self._create_search_query(symbol)
            
            # Search for news
            news_results = list(self.ddgs.news(
                keywords=search_query,
                region='in-en',  # India English for Indian markets
                safesearch='moderate',
                timelimit='d',  # Last day
                max_results=max_results
            ))
            
            # Process and format results
            formatted_news = []
            for article in news_results:
                formatted_article = {
                    "title": article.get("title", ""),
                    "url": article.get("url", ""),  # Real URL from news source
                    "source": article.get("source", ""),
                    "date": article.get("date", ""),
                    "body": article.get("body", "")[:300] + "..." if len(article.get("body", "")) > 300 else article.get("body", ""),
                    "relevance_score": self._calculate_relevance(article.get("title", ""), symbol)
                }
                formatted_news.append(formatted_article)
            
            # Sort by relevance and date
            formatted_news.sort(key=lambda x: (x["relevance_score"], x["date"]), reverse=True)
            
            return {
                "symbol": symbol,
                "search_query": search_query,
                "news_count": len(formatted_news),
                "news_articles": formatted_news,
                "data_source": "DuckDuckGo News Search",
                "timestamp": datetime.now().isoformat(),
                "success": True
            }
            
        except Exception as e:
            logger.error(f"Error fetching news for {symbol}: {str(e)}")
            return {
                "symbol": symbol,
                "error": f"Failed to fetch news: {str(e)}",
                "success": False
            }
    
    def get_fii_dii_news(self) -> Dict[str, Any]:
        """Get FII/DII flow related news using DuckDuckGo search"""
        try:
            search_query = "FII DII flows NSE India institutional investors today"
            
            news_results = list(self.ddgs.news(
                keywords=search_query,
                region='in-en',
                safesearch='moderate',
                timelimit='d',
                max_results=8
            ))
            
            formatted_news = []
            for article in news_results:
                formatted_article = {
                    "title": article.get("title", ""),
                    "url": article.get("url", ""),
                    "source": article.get("source", ""),
                    "date": article.get("date", ""),
                    "body": article.get("body", "")[:200] + "..." if len(article.get("body", "")) > 200 else article.get("body", ""),
                    "relevance_score": self._calculate_fii_dii_relevance(article.get("title", ""))
                }
                formatted_news.append(formatted_article)
            
            # Sort by relevance
            formatted_news.sort(key=lambda x: x["relevance_score"], reverse=True)
            
            return {
                "search_query": search_query,
                "news_count": len(formatted_news),
                "fii_dii_news": formatted_news,
                "data_source": "DuckDuckGo News Search",
                "timestamp": datetime.now().isoformat(),
                "success": True
            }
            
        except Exception as e:
            logger.error(f"Error fetching FII/DII news: {str(e)}")
            return {
                "error": f"Failed to fetch FII/DII news: {str(e)}",
                "success": False
            }
    
    def get_comprehensive_market_news(self, symbol: str = None, max_results: int = 15) -> Dict[str, Any]:
        """
        Get comprehensive market news including general market context
        
        Args:
            symbol: Optional specific symbol to focus on
            max_results: Maximum number of news articles
            
        Returns:
            Comprehensive news summary with real URLs
        """
        try:
            all_news = []
            
            # Get symbol-specific news if provided
            if symbol:
                symbol_news = self.get_market_news(symbol, max_results // 2)
                if symbol_news.get("success"):
                    all_news.extend(symbol_news.get("news_articles", []))
            
            # Get general market news
            market_queries = [
                "Indian stock market today Nifty Bank Nifty",
                "global markets impact India trading",
                "RBI monetary policy market impact"
            ]
            
            for query in market_queries:
                try:
                    results = list(self.ddgs.news(
                        keywords=query,
                        region='in-en',
                        safesearch='moderate',
                        timelimit='d',
                        max_results=5
                    ))
                    
                    for article in results:
                        formatted_article = {
                            "title": article.get("title", ""),
                            "url": article.get("url", ""),
                            "source": article.get("source", ""),
                            "date": article.get("date", ""),
                            "body": article.get("body", "")[:250] + "..." if len(article.get("body", "")) > 250 else article.get("body", ""),
                            "category": "market_context",
                            "relevance_score": self._calculate_market_relevance(article.get("title", ""))
                        }
                        all_news.append(formatted_article)
                        
                except Exception as e:
                    logger.warning(f"Error with query '{query}': {str(e)}")
                    continue
            
            # Remove duplicates and sort
            unique_news = self._remove_duplicates(all_news)
            unique_news.sort(key=lambda x: (x["relevance_score"], x["date"]), reverse=True)
            
            # Limit results
            final_news = unique_news[:max_results]
            
            return {
                "symbol": symbol,
                "news_count": len(final_news),
                "comprehensive_news": final_news,
                "categories": list(set([article.get("category", "general") for article in final_news])),
                "data_source": "DuckDuckGo Comprehensive Search",
                "timestamp": datetime.now().isoformat(),
                "success": True
            }
            
        except Exception as e:
            logger.error(f"Error fetching comprehensive news: {str(e)}")
            return {
                "error": f"Failed to fetch comprehensive news: {str(e)}",
                "success": False
            }
    
    def _create_search_query(self, symbol: str) -> str:
        """Create optimized search query based on symbol"""
        symbol_upper = symbol.upper()
        
        # Indian market symbols
        if symbol_upper in ['^NSEI', 'NSEI']:
            return "Nifty 50 index India stock market today"
        elif symbol_upper in ['^NSEBANK', 'NSEBANK']:
            return "Bank Nifty index banking stocks India today"
        elif symbol_upper in ['^BSESN', 'BSESN']:
            return "Sensex BSE India stock market today"
        
        # Crypto symbols
        elif 'BTC' in symbol_upper:
            return "Bitcoin BTC price crypto market today"
        elif 'ETH' in symbol_upper:
            return "Ethereum ETH price crypto market today"
        elif 'SOL' in symbol_upper:
            return "Solana SOL price crypto market today"
        
        # Default for other symbols
        else:
            clean_symbol = symbol_upper.replace('^', '').replace('.NS', '').replace('-USD', '')
            return f"{clean_symbol} stock market news today"
    
    def _calculate_relevance(self, title: str, symbol: str) -> float:
        """Calculate relevance score for news article"""
        title_lower = title.lower()
        symbol_lower = symbol.lower()
        score = 0.0
        
        # Direct symbol match
        if symbol_lower.replace('^', '').replace('.ns', '') in title_lower:
            score += 1.0
        
        # Market keywords
        market_keywords = ['market', 'trading', 'stock', 'index', 'nifty', 'sensex', 'crypto', 'bitcoin']
        for keyword in market_keywords:
            if keyword in title_lower:
                score += 0.2
        
        # Time relevance
        time_keywords = ['today', 'latest', 'breaking', 'live', 'now']
        for keyword in time_keywords:
            if keyword in title_lower:
                score += 0.1
        
        return min(score, 2.0)  # Cap at 2.0
    
    def _calculate_fii_dii_relevance(self, title: str) -> float:
        """Calculate relevance for FII/DII news"""
        title_lower = title.lower()
        score = 0.0
        
        # FII/DII keywords
        fii_dii_keywords = ['fii', 'dii', 'foreign', 'institutional', 'flows', 'investment']
        for keyword in fii_dii_keywords:
            if keyword in title_lower:
                score += 0.5
        
        # Market impact keywords
        impact_keywords = ['market', 'nifty', 'sensex', 'equity', 'buying', 'selling']
        for keyword in impact_keywords:
            if keyword in title_lower:
                score += 0.2
        
        return min(score, 2.0)
    
    def _calculate_market_relevance(self, title: str) -> float:
        """Calculate relevance for general market news"""
        title_lower = title.lower()
        score = 0.0
        
        # High priority keywords
        high_priority = ['rbi', 'fed', 'policy', 'rate', 'inflation', 'gdp']
        for keyword in high_priority:
            if keyword in title_lower:
                score += 0.8
        
        # Medium priority keywords
        medium_priority = ['market', 'nifty', 'sensex', 'banking', 'it sector']
        for keyword in medium_priority:
            if keyword in title_lower:
                score += 0.4
        
        return min(score, 2.0)
    
    def _remove_duplicates(self, news_list: List[Dict]) -> List[Dict]:
        """Remove duplicate news articles based on title similarity"""
        unique_news = []
        seen_titles = set()
        
        for article in news_list:
            title = article.get("title", "").lower()
            # Simple duplicate detection based on first 50 characters
            title_key = title[:50]
            
            if title_key not in seen_titles:
                seen_titles.add(title_key)
                unique_news.append(article)
        
        return unique_news

# Global instance
duckduckgo_news = DuckDuckGoNewsProvider()
