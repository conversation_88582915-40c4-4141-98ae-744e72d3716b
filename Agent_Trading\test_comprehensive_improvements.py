#!/usr/bin/env python3
"""
Comprehensive test for all trading app improvements:
1. Analysis History System
2. Beautiful Tool Results Display
3. Enhanced Summarization Prompts
4. Advanced Trading Tools
5. Risk Management Features
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from Agent_Trading.helpers.tool_manager import get_tool_registry
from Agent_Trading.helpers.advanced_trading_tools import (
    RiskManager, TradingSignalGenerator, PerformanceTracker,
    get_risk_management_analysis, get_trading_signals
)
from Agent_Trading.helpers.parallel_utils import _llm_summarize_tool_result
import json

def test_advanced_trading_tools():
    """Test the new advanced trading tools."""
    print("🧪 Testing Advanced Trading Tools...")
    
    # Test Risk Manager
    risk_manager = RiskManager(account_balance=50000, max_risk_per_trade=0.02)
    
    # Test position sizing
    position_data = risk_manager.calculate_position_size(
        entry_price=100.0,
        stop_loss=95.0,
        risk_amount=1000
    )
    print(f"✅ Position Sizing: {position_data}")
    
    # Test risk/reward calculation
    risk_reward = risk_manager.calculate_risk_reward(
        entry_price=100.0,
        stop_loss=95.0,
        take_profit=110.0
    )
    print(f"✅ Risk/Reward: {risk_reward}")
    
    # Test comprehensive risk analysis
    risk_analysis = get_risk_management_analysis(
        symbol="BTC-USD",
        entry_price=45000,
        stop_loss=43000,
        take_profit=50000,
        account_balance=100000
    )
    print(f"✅ Risk Analysis: {json.dumps(risk_analysis, indent=2)}")

def test_tool_registry_enhancements():
    """Test the enhanced tool registry with new tools."""
    print("\n🧪 Testing Enhanced Tool Registry...")
    
    registry = get_tool_registry()
    
    # Test that new tools are registered
    tool_names = registry.get_tool_names()
    print(f"✅ Total tools registered: {len(tool_names)}")
    
    expected_new_tools = [
        "get_risk_management_analysis",
        "get_advanced_trading_signals"
    ]
    
    for tool in expected_new_tools:
        if tool in tool_names:
            print(f"✅ {tool} - Registered")
        else:
            print(f"❌ {tool} - Missing")
    
    # Test risk management tool
    try:
        result = registry.execute_tool("get_risk_management_analysis", 
                                     symbol="BTC-USD", 
                                     entry_price=45000, 
                                     stop_loss=43000, 
                                     take_profit=50000)
        if result.success:
            print(f"✅ Risk Management Tool: Working - {len(str(result.data))} chars")
        else:
            print(f"❌ Risk Management Tool: Failed - {result.error}")
    except Exception as e:
        print(f"❌ Risk Management Tool: Exception - {e}")

def test_enhanced_summarization():
    """Test the enhanced summarization prompts."""
    print("\n🧪 Testing Enhanced Summarization...")
    
    # Test market context summarization
    sample_market_data = {
        "current_price": 45000,
        "price_change_24h": 2.5,
        "volume": 1500000000,
        "support": 43000,
        "resistance": 47000,
        "rsi": 65,
        "trend": "bullish"
    }
    
    try:
        summary = _llm_summarize_tool_result("get_market_context_summary", sample_market_data)
        print(f"✅ Market Context Summary: {len(summary)} chars")
        print(f"Preview: {summary[:100]}...")
    except Exception as e:
        print(f"❌ Market Context Summary: Failed - {e}")
    
    # Test news summarization
    sample_news_data = {
        "news": [
            {
                "title": "Bitcoin Reaches New High",
                "publisher": "CoinDesk",
                "summary": "Bitcoin price surged to new highs amid institutional adoption",
                "sentiment": "bullish"
            },
            {
                "title": "Fed Announces Rate Decision",
                "publisher": "Reuters",
                "summary": "Federal Reserve maintains current interest rates",
                "sentiment": "neutral"
            }
        ]
    }
    
    try:
        news_summary = _llm_summarize_tool_result("get_comprehensive_market_news", sample_news_data)
        print(f"✅ News Summary: {len(news_summary)} chars")
        print(f"Preview: {news_summary[:100]}...")
    except Exception as e:
        print(f"❌ News Summary: Failed - {e}")

def test_performance_tracker():
    """Test the performance tracking system."""
    print("\n🧪 Testing Performance Tracker...")
    
    tracker = PerformanceTracker()
    
    # Add sample trades
    sample_trades = [
        {"symbol": "BTC-USD", "profit_loss": 500, "entry": 44000, "exit": 45000},
        {"symbol": "ETH-USD", "profit_loss": -200, "entry": 3000, "exit": 2950},
        {"symbol": "BTC-USD", "profit_loss": 800, "entry": 45000, "exit": 46000},
        {"symbol": "SOL-USD", "profit_loss": 300, "entry": 100, "exit": 105},
        {"symbol": "ETH-USD", "profit_loss": -150, "entry": 2950, "exit": 2900}
    ]
    
    for trade in sample_trades:
        trade_id = tracker.add_trade(trade)
        print(f"✅ Added trade: {trade_id}")
    
    # Calculate performance metrics
    metrics = tracker.calculate_performance_metrics()
    print(f"✅ Performance Metrics: {json.dumps(metrics, indent=2)}")

def test_signal_generator():
    """Test the trading signal generator."""
    print("\n🧪 Testing Signal Generator...")
    
    generator = TradingSignalGenerator()
    
    # Sample market and technical data
    market_data = {
        "current_price": 45000,
        "volume": 1500000000
    }
    
    technical_data = {
        "technical_indicators": {
            "rsi": 25,  # Oversold
            "sma_20": 44000,
            "sma_50": 43000
        },
        "support_resistance": {
            "support": 43500,
            "resistance": 47000
        }
    }
    
    signals = generator.generate_entry_signals(market_data, technical_data)
    print(f"✅ Generated Signals: {json.dumps(signals, indent=2)}")

def main():
    """Run all comprehensive tests."""
    print("🚀 COMPREHENSIVE TRADING APP IMPROVEMENTS TEST")
    print("=" * 60)
    
    try:
        test_advanced_trading_tools()
        test_tool_registry_enhancements()
        test_enhanced_summarization()
        test_performance_tracker()
        test_signal_generator()
        
        print("\n" + "=" * 60)
        print("🎉 ALL TESTS COMPLETED!")
        print("✅ Analysis History System: Ready (session state)")
        print("✅ Beautiful Tool Results: Ready (display function)")
        print("✅ Enhanced Summarization: Ready (improved prompts)")
        print("✅ Advanced Trading Tools: Ready (risk management)")
        print("✅ Performance Tracking: Ready (metrics system)")
        print("✅ Signal Generation: Ready (entry/exit levels)")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
