#!/usr/bin/env python3
"""Test script to verify tool initialization and LLM setup."""

import os
import sys

# Add the project root to the Python path
script_dir = os.path.dirname(__file__)
if script_dir not in sys.path:
    sys.path.insert(0, script_dir)

def test_tool_registry():
    """Test the tool registry initialization."""
    print("🔧 Testing Tool Registry...")
    
    from Agent_Trading.helpers.tool_manager import get_tool_registry
    
    registry = get_tool_registry()
    tool_names = registry.get_tool_names()
    tool_definitions = registry.get_tool_definitions()
    
    print(f"✅ Found {len(tool_names)} registered tools:")
    for i, tool_name in enumerate(tool_names[:10], 1):  # Show first 10
        print(f"   {i}. {tool_name}")
    
    if len(tool_names) > 10:
        print(f"   ... and {len(tool_names) - 10} more")
    
    print(f"✅ Generated {len(tool_definitions)} tool definitions for LLM")
    
    return len(tool_names) > 0 and len(tool_definitions) > 0


def test_model_initialization():
    """Test LLM model initialization with tools."""
    print("\n🤖 Testing Model Initialization...")
    
    try:
        import google.generativeai as genai
        from Agent_Trading.helpers.tool_manager import get_tool_registry
        
        # Test without API key first - just check if we can create the model structure
        registry = get_tool_registry()
        tool_definitions = registry.get_tool_definitions()
        
        print(f"✅ Tool definitions ready: {len(tool_definitions)} tools")
        print("✅ Model structure can be created (API key not tested)")
        
        return True
        
    except Exception as e:
        print(f"❌ Model initialization failed: {e}")
        return False


def test_tool_function_creation():
    """Test tool function creation."""
    print("\n🔧 Testing Tool Function Creation...")
    
    try:
        from Agent_Trading.helpers.tool_manager import get_tool_registry
        
        registry = get_tool_registry()
        tool_names = registry.get_tool_names()
        
        # Create tool functions like in utils.py
        tool_functions = {}
        for tool_name in tool_names:
            def create_tool_function(name):
                def tool_function(**kwargs):
                    try:
                        result = registry.execute_tool(name, **kwargs)
                        if result.success:
                            return result.data
                        else:
                            raise Exception(f"Tool {name} failed: {result.error}")
                    except Exception as e:
                        raise Exception(f"Tool execution error for {name}: {str(e)}")
                return tool_function
            tool_functions[tool_name] = create_tool_function(tool_name)
        
        print(f"✅ Created {len(tool_functions)} tool functions")
        
        # Test a simple tool execution (this should work without external APIs)
        if 'detect_trading_symbol' in tool_functions:
            try:
                result = tool_functions['detect_trading_symbol'](text_input="BANKNIFTY")
                print(f"✅ Test tool execution successful: {result}")
            except Exception as e:
                print(f"⚠️ Test tool execution failed: {e}")
        
        return len(tool_functions) > 0
        
    except Exception as e:
        print(f"❌ Tool function creation failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 Testing Trading Agent Tool Initialization")
    print("=" * 50)
    
    tests = [
        ("Tool Registry", test_tool_registry),
        ("Model Initialization", test_model_initialization),
        ("Tool Function Creation", test_tool_function_creation),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! The LLM should be able to analyze charts with tools.")
    else:
        print("⚠️ Some tests failed. Check the issues above.")


if __name__ == "__main__":
    main()
