"""
Professional Data Normalizer
============================

Normalizes and standardizes market data:
- Price and volume normalization
- Timestamp standardization
- Data format consistency
- Currency conversion support
"""

import pandas as pd
import numpy as np
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
import logging

import sys
from pathlib import Path

# Add root directory to path for core imports
root_dir = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(root_dir))

from core.logger import get_logger

class DataNormalizer:
    """Professional data normalization for trading system"""

    def __init__(self):
        """Initialize data normalizer"""
        self.logger = get_logger()

        # Normalization parameters
        self.price_precision = 2  # Decimal places for prices
        self.volume_precision = 6  # Decimal places for volume

        self.logger.info("✅ Data normalizer initialized")

    def normalize_ohlcv_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Normalize OHLCV data to standard format

        Args:
            df: Raw OHLCV DataFrame

        Returns:
            Normalized DataFrame
        """
        if df.empty:
            return df

        try:
            normalized_df = df.copy()

            # 1. Standardize column names
            normalized_df = self._standardize_columns(normalized_df)

            # 2. Normalize timestamps
            normalized_df = self._normalize_timestamps(normalized_df)

            # 3. Normalize price data
            normalized_df = self._normalize_prices(normalized_df)

            # 4. Normalize volume data
            normalized_df = self._normalize_volume(normalized_df)

            # 5. Sort by timestamp
            normalized_df = normalized_df.sort_index()

            # 6. Remove duplicates
            normalized_df = normalized_df[~normalized_df.index.duplicated(keep='last')]

            self.logger.info(f"✅ Normalized {len(normalized_df)} records")
            return normalized_df

        except Exception as e:
            self.logger.error(f"❌ Failed to normalize data: {e}")
            return df

    def _standardize_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Standardize column names to lowercase OHLCV format"""
        column_mapping = {
            'Open': 'open',
            'High': 'high',
            'Low': 'low',
            'Close': 'close',
            'Volume': 'volume',
            'OPEN': 'open',
            'HIGH': 'high',
            'LOW': 'low',
            'CLOSE': 'close',
            'VOLUME': 'volume',
            'o': 'open',
            'h': 'high',
            'l': 'low',
            'c': 'close',
            'v': 'volume'
        }

        # Rename columns if they exist
        for old_name, new_name in column_mapping.items():
            if old_name in df.columns:
                df = df.rename(columns={old_name: new_name})

        return df

    def _normalize_timestamps(self, df: pd.DataFrame) -> pd.DataFrame:
        """Normalize timestamps to UTC datetime index"""
        try:
            # If index is not datetime, try to convert
            if not isinstance(df.index, pd.DatetimeIndex):
                # Check if there's a timestamp column
                timestamp_cols = ['timestamp', 'time', 'datetime', 'date']
                timestamp_col = None

                for col in timestamp_cols:
                    if col in df.columns:
                        timestamp_col = col
                        break

                if timestamp_col:
                    # Convert timestamp column to datetime index
                    df[timestamp_col] = pd.to_datetime(df[timestamp_col])
                    df = df.set_index(timestamp_col)
                    df.index.name = 'timestamp'
                else:
                    # If no timestamp column, create one based on row order
                    self.logger.warning("⚠️ No timestamp column found, creating sequential timestamps")
                    end_time = datetime.utcnow()
                    periods = len(df)
                    df.index = pd.date_range(end=end_time, periods=periods, freq='3T')
                    df.index.name = 'timestamp'

            # Ensure timezone awareness (convert to UTC)
            if df.index.tz is None:
                df.index = df.index.tz_localize('UTC')
            else:
                df.index = df.index.tz_convert('UTC')

            return df

        except Exception as e:
            self.logger.error(f"❌ Failed to normalize timestamps: {e}")
            return df

    def _normalize_prices(self, df: pd.DataFrame) -> pd.DataFrame:
        """Normalize price data (OHLC)"""
        price_columns = ['open', 'high', 'low', 'close']

        for col in price_columns:
            if col in df.columns:
                # Convert to float and round to specified precision
                df[col] = pd.to_numeric(df[col], errors='coerce')
                df[col] = df[col].round(self.price_precision)

                # Ensure positive prices
                df[col] = df[col].abs()

        return df

    def _normalize_volume(self, df: pd.DataFrame) -> pd.DataFrame:
        """Normalize volume data"""
        if 'volume' in df.columns:
            # Convert to float and round to specified precision
            df['volume'] = pd.to_numeric(df['volume'], errors='coerce')
            df['volume'] = df['volume'].round(self.volume_precision)

            # Ensure non-negative volume
            df['volume'] = df['volume'].abs()

            # Fill zero volumes with small positive value to avoid division issues
            df['volume'] = df['volume'].replace(0, 0.000001)

        return df

    def add_derived_fields(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add derived fields to the data

        Args:
            df: Normalized OHLCV DataFrame

        Returns:
            DataFrame with additional fields
        """
        if df.empty:
            return df

        try:
            enhanced_df = df.copy()

            # 1. Price-based fields
            enhanced_df['typical_price'] = (enhanced_df['high'] + enhanced_df['low'] + enhanced_df['close']) / 3
            enhanced_df['median_price'] = (enhanced_df['high'] + enhanced_df['low']) / 2
            enhanced_df['weighted_price'] = (enhanced_df['high'] + enhanced_df['low'] + 2 * enhanced_df['close']) / 4

            # 2. Range and volatility
            enhanced_df['price_range'] = enhanced_df['high'] - enhanced_df['low']
            enhanced_df['price_range_pct'] = (enhanced_df['price_range'] / enhanced_df['close']) * 100

            # 3. Body and wick analysis
            enhanced_df['body_size'] = abs(enhanced_df['close'] - enhanced_df['open'])
            enhanced_df['upper_wick'] = enhanced_df['high'] - np.maximum(enhanced_df['open'], enhanced_df['close'])
            enhanced_df['lower_wick'] = np.minimum(enhanced_df['open'], enhanced_df['close']) - enhanced_df['low']

            # 4. Price changes
            enhanced_df['price_change'] = enhanced_df['close'].diff()
            enhanced_df['price_change_pct'] = enhanced_df['close'].pct_change() * 100

            # 5. Volume-price indicators
            enhanced_df['vwap_component'] = enhanced_df['typical_price'] * enhanced_df['volume']
            enhanced_df['volume_change'] = enhanced_df['volume'].diff()
            enhanced_df['volume_change_pct'] = enhanced_df['volume'].pct_change() * 100

            # 6. Trend indicators
            enhanced_df['is_green'] = enhanced_df['close'] > enhanced_df['open']
            enhanced_df['is_red'] = enhanced_df['close'] < enhanced_df['open']
            enhanced_df['is_doji'] = abs(enhanced_df['close'] - enhanced_df['open']) < (enhanced_df['price_range'] * 0.1)

            self.logger.info(f"✅ Added derived fields to {len(enhanced_df)} records")
            return enhanced_df

        except Exception as e:
            self.logger.error(f"❌ Failed to add derived fields: {e}")
            return df

    def normalize_for_ml(self, df: pd.DataFrame,
                        features: Optional[List[str]] = None) -> pd.DataFrame:
        """Normalize data for machine learning

        Args:
            df: Input DataFrame
            features: List of features to normalize (default: price columns)

        Returns:
            ML-ready normalized DataFrame
        """
        if df.empty:
            return df

        try:
            if features is None:
                features = ['open', 'high', 'low', 'close', 'volume']

            ml_df = df.copy()

            # Z-score normalization for each feature
            for feature in features:
                if feature in ml_df.columns:
                    mean_val = ml_df[feature].mean()
                    std_val = ml_df[feature].std()

                    if std_val > 0:
                        ml_df[f'{feature}_normalized'] = (ml_df[feature] - mean_val) / std_val
                    else:
                        ml_df[f'{feature}_normalized'] = 0

            # Min-Max normalization (0-1 scale)
            for feature in features:
                if feature in ml_df.columns:
                    min_val = ml_df[feature].min()
                    max_val = ml_df[feature].max()

                    if max_val > min_val:
                        ml_df[f'{feature}_minmax'] = (ml_df[feature] - min_val) / (max_val - min_val)
                    else:
                        ml_df[f'{feature}_minmax'] = 0.5

            self.logger.info(f"✅ ML normalization completed for {len(features)} features")
            return ml_df

        except Exception as e:
            self.logger.error(f"❌ Failed to normalize for ML: {e}")
            return df

    def validate_normalized_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Validate normalized data quality

        Args:
            df: Normalized DataFrame

        Returns:
            Validation report
        """
        report = {
            'is_valid': True,
            'issues': [],
            'statistics': {}
        }

        if df.empty:
            report['is_valid'] = False
            report['issues'].append("DataFrame is empty")
            return report

        try:
            # Check required columns
            required_cols = ['open', 'high', 'low', 'close', 'volume']
            missing_cols = [col for col in required_cols if col not in df.columns]

            if missing_cols:
                report['is_valid'] = False
                report['issues'].append(f"Missing columns: {missing_cols}")

            # Check datetime index
            if not isinstance(df.index, pd.DatetimeIndex):
                report['is_valid'] = False
                report['issues'].append("Index is not DatetimeIndex")

            # Check for NaN values
            nan_counts = df.isnull().sum()
            if nan_counts.sum() > 0:
                report['issues'].append(f"NaN values found: {nan_counts.to_dict()}")

            # Check price relationships
            if all(col in df.columns for col in ['open', 'high', 'low', 'close']):
                invalid_ohlc = len(df[
                    (df['high'] < df['low']) |
                    (df['high'] < df['open']) |
                    (df['high'] < df['close']) |
                    (df['low'] > df['open']) |
                    (df['low'] > df['close'])
                ])

                if invalid_ohlc > 0:
                    report['is_valid'] = False
                    report['issues'].append(f"Invalid OHLC relationships: {invalid_ohlc} records")

            # Generate statistics
            for col in required_cols:
                if col in df.columns:
                    report['statistics'][col] = {
                        'mean': df[col].mean(),
                        'std': df[col].std(),
                        'min': df[col].min(),
                        'max': df[col].max(),
                        'null_count': df[col].isnull().sum()
                    }

            return report

        except Exception as e:
            report['is_valid'] = False
            report['issues'].append(f"Validation error: {str(e)}")
            return report
