"""
Parallel tool execution utilities for faster analysis.
This module provides optimized tool execution with parallel processing and result summarization.
"""

import asyncio
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Callable
import json
from datetime import datetime, timedelta
import google.generativeai as genai
import logging
from .quota_manager import quota_manager
from .tool_quality_enhancer import tool_quality_enhancer
# Removed indian_market_tools import to avoid NumPy conflicts
from .prompts import tool_summarization_prompts, generic_summarization_prompt
import os


logger = logging.getLogger(__name__)


class ToolResultCache:
    """Simple in-memory cache for tool results."""
    
    def __init__(self, ttl_minutes: int = 5):
        self.cache = {}
        self.ttl_minutes = ttl_minutes
    
    def get(self, key: str) -> Any:
        """Get cached result if not expired."""
        if key in self.cache:
            result, timestamp = self.cache[key]
            if datetime.now() - timestamp < timedelta(minutes=self.ttl_minutes):
                return result
            else:
                del self.cache[key]
        return None
    
    def set(self, key: str, value: Any):
        """Cache a result with timestamp."""
        self.cache[key] = (value, datetime.now())
    
    def clear_expired(self):
        """Remove expired entries."""
        now = datetime.now()
        expired_keys = [
            key for key, (_, timestamp) in self.cache.items()
            if now - timestamp >= timedelta(minutes=self.ttl_minutes)
        ]
        for key in expired_keys:
            del self.cache[key]


# Global cache instance
tool_cache = ToolResultCache(ttl_minutes=5)


def summarize_tool_result(tool_name: str, result: Any, use_llm_summary: bool = True) -> str:
    """Summarize tool results using LLM or fallback to template-based summarization."""
    try:
        # Convert to string to check length
        result_str = str(result)

        # If result is short enough, return as-is
        if len(result_str) <= 200:
            return result_str

        # Try LLM summarization first for better quality
        if use_llm_summary and len(result_str) > 300:
            try:
                return _llm_summarize_tool_result(tool_name, result)
            except Exception as e:
                print(f"⚠️ LLM summarization failed for {tool_name}: {e}")
                # Fall back to template-based summarization

        # Template-based summarization (existing logic)
        if isinstance(result, dict):
            if "news" in tool_name.lower():
                return summarize_news_result(result)
            elif "market" in tool_name.lower() or "data" in tool_name.lower():
                return summarize_market_result(result)
            elif "flow" in tool_name.lower():
                return summarize_flow_result(result)
            else:
                return summarize_generic_result(result)
        else:
            # Convert to string and truncate if too long
            return result_str[:500] + "..." if len(result_str) > 500 else result_str
    except Exception as e:
        return f"Error summarizing {tool_name}: {str(e)}"


def _llm_summarize_tool_result(tool_name: str, result_data: Any) -> str:
    """
    Use Gemini 2.5 Flash for tool summarization with automatic fallback to 2.0 Flash.
    This is backend logic - user doesn't select this model.
    """

    # Check quota before making API call
    if not quota_manager.can_make_request():
        logger.warning(f"API quota exceeded, using fallback summarization for {tool_name}")
        return quota_manager.fallback_summarize(tool_name, result_data)

    # Configure API key (same as main analysis)
    try:
        # Try to load from config.json first
        config_path = os.path.join(os.path.dirname(__file__), '..', 'config.json')
        api_key = None

        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config = json.load(f)
                api_key = config.get('google_api_key')

        # Fallback to environment variable
        if not api_key:
            api_key = os.getenv('GOOGLE_API_KEY')

        if not api_key:
            raise Exception("No Google API key found in config.json or environment")

        genai.configure(api_key=api_key)
    except Exception as e:
        logger.error(f"Failed to configure API: {e}")
        return quota_manager.fallback_summarize(tool_name, result_data)

    # Model fallback chain for tool summarization (backend logic)
    summarization_models = ["gemini-2.5-flash", "gemini-2.0-flash-exp"]

    for model_name in summarization_models:
        try:
            flash_model = genai.GenerativeModel(model_name)
            logger.info(f"Using {model_name} for tool summarization")
            break
        except Exception as e:
            logger.warning(f"Failed to initialize {model_name}: {e}")
            if model_name == summarization_models[-1]:  # Last model failed
                logger.error("All summarization models failed, using fallback")
                return quota_manager.fallback_summarize(tool_name, result_data)
            continue

    # Get appropriate prompt from prompts module with better data handling
    try:
        # Safely convert result_data to string
        if isinstance(result_data, dict):
            if 'enhanced_data' in result_data:
                data_str = json.dumps(result_data['enhanced_data'], indent=2, default=str)
            else:
                data_str = json.dumps(result_data, indent=2, default=str)
        elif isinstance(result_data, (list, tuple)):
            data_str = json.dumps(list(result_data), indent=2, default=str)
        else:
            data_str = str(result_data)

        # Truncate if too long
        if len(data_str) > 2000:
            data_str = data_str[:2000] + "... [truncated]"

    except Exception as e:
        logger.warning(f"Failed to serialize result_data for {tool_name}: {e}")
        data_str = f"[Serialization failed: {str(result_data)[:500]}]"

    prompt = tool_summarization_prompts.get(tool_name,
        generic_summarization_prompt.format(tool_name=tool_name, data=data_str)
    ).format(data=data_str)

    # Generate summary with Flash and quota tracking
    for model_name in summarization_models:
        try:
            flash_model = genai.GenerativeModel(model_name)
            response = flash_model.generate_content(prompt)
            quota_manager.increment_usage()  # Track successful API call

            # Extract response text properly with better error handling
            response_text = ""
            try:
                if hasattr(response, 'text') and response.text:
                    response_text = response.text
                elif hasattr(response, 'candidates') and response.candidates:
                    candidate = response.candidates[0]
                    if hasattr(candidate, 'content') and hasattr(candidate.content, 'parts'):
                        if candidate.content.parts:
                            response_text = candidate.content.parts[0].text
                    elif hasattr(candidate, 'text'):
                        response_text = candidate.text
                    else:
                        response_text = str(candidate)
                else:
                    # Fallback to string conversion
                    response_text = str(response)

                # Clean up the response text
                if response_text and len(response_text.strip()) > 0:
                    logger.info(f"Successfully summarized {tool_name} using {model_name}")
                    return response_text.strip()
                else:
                    raise Exception("Empty response text after extraction")

            except Exception as extract_error:
                logger.warning(f"Response text extraction failed: {extract_error}")
                raise extract_error

        except Exception as e:
            logger.warning(f"Summarization failed with {model_name}: {e}")
            if model_name == summarization_models[-1]:  # Last model failed
                logger.error(f"All summarization models failed for {tool_name}: {e}")
                # Fallback to template-based summary
                return quota_manager.fallback_summarize(tool_name, result_data)
            continue  # Try next model


def summarize_news_result(result: Dict) -> str:
    """Summarize news tool results."""
    try:
        summary = "📰 News Summary:\n"
        if "articles" in result:
            articles = result["articles"][:3]  # Top 3 articles
            for i, article in enumerate(articles, 1):
                title = article.get("title", "No title")[:100]
                summary += f"{i}. {title}\n"
        
        if "sentiment" in result:
            summary += f"Overall Sentiment: {result['sentiment']}\n"
        
        return summary[:400] + "..." if len(summary) > 400 else summary
    except:
        return str(result)[:300]


def summarize_market_result(result: Dict) -> str:
    """Summarize market data tool results."""
    try:
        summary = "📊 Market Data:\n"
        
        # Key price metrics
        if "current_price" in result:
            summary += f"Price: {result['current_price']}\n"
        if "change_percent" in result:
            summary += f"Change: {result['change_percent']}%\n"
        if "volume" in result:
            summary += f"Volume: {result['volume']}\n"
        
        # Support/Resistance levels
        if "support_levels" in result:
            levels = result["support_levels"][:2]  # Top 2 levels
            summary += f"Support: {', '.join(map(str, levels))}\n"
        if "resistance_levels" in result:
            levels = result["resistance_levels"][:2]  # Top 2 levels
            summary += f"Resistance: {', '.join(map(str, levels))}\n"
        
        return summary[:300]
    except:
        return str(result)[:300]


def summarize_flow_result(result: Dict) -> str:
    """Summarize FII/DII flow results."""
    try:
        summary = "💰 Flow Data:\n"
        
        if "fii_flow" in result:
            summary += f"FII: {result['fii_flow']}\n"
        if "dii_flow" in result:
            summary += f"DII: {result['dii_flow']}\n"
        if "net_flow" in result:
            summary += f"Net: {result['net_flow']}\n"
        
        return summary[:200]
    except:
        return str(result)[:200]


def summarize_generic_result(result: Dict) -> str:
    """Summarize generic tool results."""
    try:
        # Extract key-value pairs, prioritizing important keys
        important_keys = ["price", "volume", "change", "trend", "signal", "recommendation"]
        summary = ""
        
        for key in important_keys:
            if key in result:
                summary += f"{key}: {result[key]}\n"
        
        # Add other keys if space allows
        for key, value in result.items():
            if key not in important_keys and len(summary) < 200:
                summary += f"{key}: {str(value)[:50]}\n"
        
        return summary[:300]
    except:
        return str(result)[:300]


def execute_tools_parallel(tool_calls: List[Dict], tool_functions: Dict, progress_callback: Callable = None) -> Dict[str, Any]:
    """Execute multiple tools in parallel with caching and summarization."""

    def execute_single_tool(tool_call: Dict) -> Dict[str, Any]:
        """Execute a single tool with caching."""
        tool_name = tool_call["name"]
        tool_args = tool_call.get("args", {})
        
        # Create cache key
        cache_key = f"{tool_name}:{json.dumps(tool_args, sort_keys=True)}"
        
        # Check cache first
        cached_result = tool_cache.get(cache_key)
        if cached_result is not None:
            if progress_callback:
                progress_callback(f"📋 Using cached result for {tool_name}")
            return {
                "tool_name": tool_name,
                "result": cached_result,
                "cached": True,
                "timestamp": datetime.now().isoformat()
            }
        
        # Execute tool with thread-safe error handling
        try:
            if progress_callback:
                progress_callback(f"🔧 Executing {tool_name}...")

            # Execute with explicit error capture
            try:
                result = tool_functions[tool_name](**tool_args)

                # Validate result is not empty
                if result is None or (isinstance(result, (str, dict, list)) and len(str(result)) == 0):
                    return {
                        "tool_name": tool_name,
                        "error": f"Tool returned empty result: {type(result)}",
                        "timestamp": datetime.now().isoformat()
                    }

            except Exception as tool_error:
                return {
                    "tool_name": tool_name,
                    "error": f"Tool execution failed: {str(tool_error)}",
                    "timestamp": datetime.now().isoformat()
                }

            # Cache the result
            tool_cache.set(cache_key, result)

            if progress_callback:
                progress_callback(f"✅ {tool_name} completed")

            # Enhance result quality and relevance
            context = {'symbol': tool_args.get('symbol')} if 'symbol' in tool_args else None
            enhanced_result = tool_quality_enhancer.enhance_tool_result(tool_name, result, context)

            return {
                "tool_name": tool_name,
                "result": result,
                "enhanced_result": enhanced_result,
                "cached": False,
                "timestamp": datetime.now().isoformat(),
                "quality_score": enhanced_result['quality_score'],
                "quality_issues": enhanced_result['quality_issues']
            }
        except Exception as e:
            return {
                "tool_name": tool_name,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    # Execute tools in parallel with fallback to sequential
    start_time = time.time()
    results = {}

    try:
        with ThreadPoolExecutor(max_workers=min(len(tool_calls), 4)) as executor:
            # Submit all tool executions
            future_to_tool = {
                executor.submit(execute_single_tool, tool_call): tool_call["name"]
                for tool_call in tool_calls
            }

            # Collect results as they complete
            for future in as_completed(future_to_tool):
                tool_name = future_to_tool[future]
                try:
                    result = future.result()
                    results[tool_name] = result
                except Exception as e:
                    results[tool_name] = {
                        "tool_name": tool_name,
                        "error": str(e),
                        "timestamp": datetime.now().isoformat()
                    }

        # Check if parallel execution failed (all results are errors or empty)
        successful_results = [r for r in results.values() if "error" not in r and r.get("result")]
        if len(successful_results) == 0 and len(tool_calls) > 0:
            if progress_callback:
                progress_callback("⚠️ Parallel execution failed, trying sequential...")

            # Fallback to sequential execution
            results = {}
            for tool_call in tool_calls:
                result = execute_single_tool(tool_call)
                results[tool_call["name"]] = result

    except Exception as e:
        if progress_callback:
            progress_callback("⚠️ Parallel execution failed, trying sequential...")

        # Fallback to sequential execution
        results = {}
        for tool_call in tool_calls:
            result = execute_single_tool(tool_call)
            results[tool_call["name"]] = result
    
    execution_time = time.time() - start_time
    
    if progress_callback:
        progress_callback(f"🚀 All tools completed in {execution_time:.1f}s")
    
    return {
        "results": results,
        "execution_time": execution_time,
        "total_tools": len(tool_calls),
        "cached_tools": sum(1 for r in results.values() if r.get("cached", False))
    }


def create_summarized_context(tool_results: Dict[str, Any]) -> str:
    """Create a summarized context from tool results for LLM."""
    context = "=== MARKET ANALYSIS DATA ===\n\n"
    
    for tool_name, tool_result in tool_results["results"].items():
        if "error" in tool_result:
            context += f"❌ {tool_name}: {tool_result['error']}\n\n"
        else:
            # Summarize the result
            summarized = summarize_tool_result(tool_name, tool_result.get("result"))
            cached_indicator = " (cached)" if tool_result.get("cached") else ""
            context += f"🔧 {tool_name}{cached_indicator}:\n{summarized}\n\n"
    
    # Add execution summary
    context += f"⚡ Execution Summary:\n"
    context += f"- Total tools: {tool_results['total_tools']}\n"
    context += f"- Cached results: {tool_results['cached_tools']}\n"
    context += f"- Execution time: {tool_results['execution_time']:.1f}s\n\n"
    
    return context
