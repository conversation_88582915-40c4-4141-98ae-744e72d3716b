# Professional Trading Dashboard Requirements
# ==========================================

# Core Dashboard Framework
streamlit>=1.28.0
plotly>=5.17.0
google-genai>=0.1.0
Pillow>=9.0.0


# Data Processing
pandas>=2.0.0
yfinance>=0.2.37
numpy>=1.21.0,<1.24 # Uncommented and set a compatible range

# Technical Analysis
# Alternative pre-compiled wheel
##https://github.com/cgohlke/talib-build/releases/download/v0.4.28/TA_Lib-0.4.28-cp310-cp310-win_amd64.whl

# Backtesting
#vectorbt==0.24.3  # ✅ Compatible with Python 3.10

# API and Networking
requests>=2.31.0
websocket-client>=1.6.0
duckduckgo-search>=6.1.0  # Fast news search with real URLs

# Utilities
python-dateutil>=2.8.0
pytz>=2023.3

# Optional: Enhanced Features
streamlit-plotly-events>=0.0.6  # For interactive charts
streamlit-aggrid>=0.3.4         # For advanced data tables
streamlit-option-menu>=0.3.6    # For enhanced navigation

