#!/usr/bin/env python3
"""Test Delta Exchange integration."""

import sys
import os
sys.path.append('.')

from Agent_Trading.helpers.delta_exchange import create_delta_client

def test_delta_integration():
    """Test Delta Exchange API integration."""
    print("🧪 Testing Delta Exchange Integration...")

    # Create client
    client = create_delta_client('Agent_Trading/config.json')
    if not client:
        print("❌ Failed to create Delta Exchange client")
        return False

    print("✅ Delta Exchange client created successfully")

    # First, let's see what symbols are available
    print("\n📋 Checking available symbols...")
    symbols = client.list_available_symbols()
    if symbols:
        print(f"✅ Found {len(symbols)} symbols")

        # Look for spot trading pairs (not options)
        spot_symbols = [s for s in symbols if not s.startswith(('C-', 'P-')) and any(crypto in s for crypto in ['BTC', 'ETH', 'SOL'])]
        print("🪙 Spot crypto symbols found:")
        for symbol in spot_symbols[:20]:  # Show first 20
            print(f"   • {symbol}")

        # Look for USDT pairs specifically
        usdt_symbols = [s for s in symbols if 'USDT' in s]
        print(f"\n💰 USDT pairs found ({len(usdt_symbols)}):")
        for symbol in usdt_symbols[:10]:
            print(f"   • {symbol}")

    else:
        print("❌ No symbols found")
    
    # Test BTC data
    print("\n📊 Testing BTC/USDT data fetch...")
    result = client.get_market_data('BTCUSDT', '1d', '1h')
    
    if 'error' in result:
        print(f"❌ Error fetching BTC data: {result['error']}")
        return False
    
    print("✅ BTC data fetched successfully")
    print(f"   Current BTC price: ${result.get('current_price', 'N/A')}")
    print(f"   24h change: {result.get('price_change_24h', 'N/A')}")
    print(f"   Data points: {result.get('summary', {}).get('total_candles', 'N/A')}")
    
    # Test ETH data
    print("\n📊 Testing ETH/USDT data fetch...")
    eth_result = client.get_market_data('ETHUSDT', '1d', '1h')

    if 'error' in eth_result:
        print(f"❌ Error fetching ETH data: {eth_result['error']}")
    else:
        print("✅ ETH data fetched successfully")
        print(f"   Current ETH price: ${eth_result.get('current_price', 'N/A')}")

    # Test SOL data
    print("\n📊 Testing SOL/USDT data fetch...")
    sol_result = client.get_market_data('SOLUSDT', '1d', '1h')
    
    if 'error' in sol_result:
        print(f"❌ Error fetching SOL data: {sol_result['error']}")
    else:
        print("✅ SOL data fetched successfully")
        print(f"   Current SOL price: ${sol_result.get('current_price', 'N/A')}")
    
    print("\n🎉 Delta Exchange integration test completed!")
    return True

if __name__ == "__main__":
    test_delta_integration()
