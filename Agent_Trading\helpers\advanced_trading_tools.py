#!/usr/bin/env python3
"""
Advanced Trading Tools for Enhanced Profitability
Features: Risk Management, Position Sizing, Entry/Exit Signals, Performance Tracking
"""

import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import math

class RiskManager:
    """Advanced risk management calculations for trading."""
    
    def __init__(self, account_balance: float = 100000, max_risk_per_trade: float = 0.02):
        self.account_balance = account_balance
        self.max_risk_per_trade = max_risk_per_trade  # 2% default
    
    def calculate_position_size(self, entry_price: float, stop_loss: float, 
                              risk_amount: Optional[float] = None) -> Dict[str, Any]:
        """Calculate optimal position size based on risk management."""
        if risk_amount is None:
            risk_amount = self.account_balance * self.max_risk_per_trade
        
        # Calculate risk per share/unit
        risk_per_unit = abs(entry_price - stop_loss)
        
        if risk_per_unit == 0:
            return {"error": "Stop loss cannot equal entry price"}
        
        # Calculate position size
        position_size = risk_amount / risk_per_unit
        position_value = position_size * entry_price
        
        # Risk/reward calculations
        return {
            "position_size": round(position_size, 2),
            "position_value": round(position_value, 2),
            "risk_amount": round(risk_amount, 2),
            "risk_per_unit": round(risk_per_unit, 2),
            "risk_percentage": round((risk_amount / self.account_balance) * 100, 2),
            "max_loss": round(risk_amount, 2),
            "account_balance": self.account_balance
        }
    
    def calculate_risk_reward(self, entry_price: float, stop_loss: float, 
                            take_profit: float) -> Dict[str, Any]:
        """Calculate risk/reward ratio and probability assessments."""
        risk = abs(entry_price - stop_loss)
        reward = abs(take_profit - entry_price)
        
        if risk == 0:
            return {"error": "Invalid risk calculation"}
        
        risk_reward_ratio = reward / risk
        
        # Kelly Criterion for position sizing (assuming 60% win rate)
        win_rate = 0.60
        kelly_percentage = (win_rate * risk_reward_ratio - (1 - win_rate)) / risk_reward_ratio
        kelly_percentage = max(0, min(kelly_percentage, 0.25))  # Cap at 25%
        
        return {
            "risk_amount": round(risk, 2),
            "reward_amount": round(reward, 2),
            "risk_reward_ratio": round(risk_reward_ratio, 2),
            "kelly_percentage": round(kelly_percentage * 100, 2),
            "recommended_position": "Strong" if risk_reward_ratio >= 3 else "Moderate" if risk_reward_ratio >= 2 else "Weak",
            "trade_quality": "Excellent" if risk_reward_ratio >= 3 else "Good" if risk_reward_ratio >= 2 else "Poor"
        }

class TradingSignalGenerator:
    """Generate specific entry/exit signals with price levels."""
    
    def generate_entry_signals(self, market_data: Dict, technical_data: Dict) -> Dict[str, Any]:
        """Generate specific entry signals with price levels."""
        signals = []
        current_price = market_data.get("current_price", 0)
        
        if not current_price:
            return {"error": "No current price data available"}
        
        # Technical analysis based signals
        tech_indicators = technical_data.get("technical_indicators", {})
        support_resistance = technical_data.get("support_resistance", {})
        
        rsi = tech_indicators.get("rsi")
        sma_20 = tech_indicators.get("sma_20")
        sma_50 = tech_indicators.get("sma_50")
        support = support_resistance.get("support")
        resistance = support_resistance.get("resistance")
        
        # Generate specific entry signals
        if rsi and rsi < 30 and support:
            entry_price = support * 1.005  # Slightly above support
            stop_loss = support * 0.98     # 2% below support
            take_profit = current_price * 1.06  # 6% target
            
            signals.append({
                "signal_type": "BUY",
                "reason": "RSI Oversold + Support Level",
                "entry_price": round(entry_price, 2),
                "stop_loss": round(stop_loss, 2),
                "take_profit": round(take_profit, 2),
                "confidence": 75,
                "timeframe": "1-3 days"
            })
        
        if rsi and rsi > 70 and resistance:
            entry_price = resistance * 0.995  # Slightly below resistance
            stop_loss = resistance * 1.02     # 2% above resistance
            take_profit = current_price * 0.94  # 6% target
            
            signals.append({
                "signal_type": "SELL",
                "reason": "RSI Overbought + Resistance Level",
                "entry_price": round(entry_price, 2),
                "stop_loss": round(stop_loss, 2),
                "take_profit": round(take_profit, 2),
                "confidence": 75,
                "timeframe": "1-3 days"
            })
        
        # Moving average crossover signals
        if sma_20 and sma_50 and current_price:
            if current_price > sma_20 > sma_50:
                signals.append({
                    "signal_type": "BUY",
                    "reason": "Bullish Trend - Price Above MAs",
                    "entry_price": round(current_price, 2),
                    "stop_loss": round(sma_20 * 0.97, 2),
                    "take_profit": round(current_price * 1.08, 2),
                    "confidence": 65,
                    "timeframe": "3-7 days"
                })
        
        return {
            "signals": signals,
            "total_signals": len(signals),
            "highest_confidence": max([s["confidence"] for s in signals]) if signals else 0,
            "recommended_action": signals[0]["signal_type"] if signals else "HOLD"
        }

class PerformanceTracker:
    """Track trading performance and provide insights."""
    
    def __init__(self):
        self.trades = []
    
    def add_trade(self, trade_data: Dict) -> str:
        """Add a completed trade for tracking."""
        trade_id = f"trade_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        trade_data["trade_id"] = trade_id
        trade_data["timestamp"] = datetime.now().isoformat()
        self.trades.append(trade_data)
        return trade_id
    
    def calculate_performance_metrics(self) -> Dict[str, Any]:
        """Calculate comprehensive performance metrics."""
        if not self.trades:
            return {"error": "No trades recorded"}
        
        total_trades = len(self.trades)
        winning_trades = [t for t in self.trades if t.get("profit_loss", 0) > 0]
        losing_trades = [t for t in self.trades if t.get("profit_loss", 0) < 0]
        
        win_rate = len(winning_trades) / total_trades * 100
        total_pnl = sum(t.get("profit_loss", 0) for t in self.trades)
        
        avg_win = sum(t["profit_loss"] for t in winning_trades) / len(winning_trades) if winning_trades else 0
        avg_loss = sum(t["profit_loss"] for t in losing_trades) / len(losing_trades) if losing_trades else 0
        
        profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')
        
        return {
            "total_trades": total_trades,
            "winning_trades": len(winning_trades),
            "losing_trades": len(losing_trades),
            "win_rate": round(win_rate, 2),
            "total_pnl": round(total_pnl, 2),
            "average_win": round(avg_win, 2),
            "average_loss": round(avg_loss, 2),
            "profit_factor": round(profit_factor, 2),
            "performance_grade": self._get_performance_grade(win_rate, profit_factor)
        }
    
    def _get_performance_grade(self, win_rate: float, profit_factor: float) -> str:
        """Grade trading performance."""
        if win_rate >= 60 and profit_factor >= 2.0:
            return "A+ (Excellent)"
        elif win_rate >= 55 and profit_factor >= 1.5:
            return "A (Very Good)"
        elif win_rate >= 50 and profit_factor >= 1.2:
            return "B (Good)"
        elif win_rate >= 45 and profit_factor >= 1.0:
            return "C (Average)"
        else:
            return "D (Needs Improvement)"

def get_risk_management_analysis(symbol: str, entry_price: float, stop_loss: float, 
                               take_profit: float, account_balance: float = 100000) -> Dict[str, Any]:
    """Get comprehensive risk management analysis."""
    risk_manager = RiskManager(account_balance)
    
    # Position sizing
    position_data = risk_manager.calculate_position_size(entry_price, stop_loss)
    
    # Risk/reward analysis
    risk_reward_data = risk_manager.calculate_risk_reward(entry_price, stop_loss, take_profit)
    
    return {
        "symbol": symbol,
        "position_sizing": position_data,
        "risk_reward": risk_reward_data,
        "recommendations": {
            "trade_viability": "Recommended" if risk_reward_data.get("risk_reward_ratio", 0) >= 2 else "Not Recommended",
            "position_adjustment": "Reduce size" if risk_reward_data.get("risk_reward_ratio", 0) < 1.5 else "Standard size",
            "risk_level": "Low" if risk_reward_data.get("risk_reward_ratio", 0) >= 3 else "Medium" if risk_reward_data.get("risk_reward_ratio", 0) >= 2 else "High"
        }
    }

def get_trading_signals(market_data: Dict, technical_data: Dict) -> Dict[str, Any]:
    """Get comprehensive trading signals with specific entry/exit levels."""
    signal_generator = TradingSignalGenerator()
    return signal_generator.generate_entry_signals(market_data, technical_data)
