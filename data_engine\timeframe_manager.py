"""Timeframe Manager
====================

Utilities for resampling and aligning market data across multiple timeframes.
"""

from __future__ import annotations

from typing import Dict
import pandas as pd

import sys
from pathlib import Path

# Add root directory to path for core imports
root_dir = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(root_dir))

from core.logger import get_logger


class TimeframeManager:
    """Handle timeframe conversions and alignment."""

    _FREQ_MAP = {
        "1m": "1T",
        "3m": "3T",
        "5m": "5T",
        "15m": "15T",
        "30m": "30T",
        "1h": "1H",
        "4h": "4H",
        "1d": "1D",
    }

    def __init__(self) -> None:
        self.logger = get_logger()
        self.logger.info("✅ Timeframe manager initialized")

    def _to_freq(self, timeframe: str) -> str:
        """Convert a timeframe like '3m' to a pandas frequency string."""
        freq = self._FREQ_MAP.get(timeframe)
        if not freq:
            raise ValueError(f"Unsupported timeframe: {timeframe}")
        return freq

    def resample_data(
        self, df: pd.DataFrame, current_timeframe: str, target_timeframe: str
    ) -> pd.DataFrame:
        """Resample data from one timeframe to another.

        Parameters
        ----------
        df : pd.DataFrame
            DataFrame indexed by timestamp with OHLCV columns.
        current_timeframe : str
            The current timeframe of ``df`` (e.g. ``'3m'``).
        target_timeframe : str
            Desired output timeframe (e.g. ``'15m'``).

        Returns
        -------
        pd.DataFrame
            Resampled OHLCV data.
        """
        if df.empty:
            return df

        cur_freq = self._to_freq(current_timeframe)
        target_freq = self._to_freq(target_timeframe)

        if cur_freq == target_freq:
            return df.copy()

        df = df.sort_index()
        resampled = (
            df.resample(target_freq)
            .agg({
                "open": "first",
                "high": "max",
                "low": "min",
                "close": "last",
                "volume": "sum",
            })
            .dropna()
        )

        self.logger.info(
            f"Resampled {len(df)} records from {current_timeframe} to {target_timeframe}"
        )
        return resampled

    def align_timeframes(self, data_dict: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """Align multiple timeframe DataFrames on a common timestamp index.

        Parameters
        ----------
        data_dict : Dict[str, pd.DataFrame]
            Mapping of timeframe label to DataFrame.

        Returns
        -------
        pd.DataFrame
            Combined DataFrame where columns are suffixed with the timeframe.
        """
        if not data_dict:
            return pd.DataFrame()

        # Determine union index
        all_indices = [df.index for df in data_dict.values() if not df.empty]
        if not all_indices:
            return pd.DataFrame()

        union_index = all_indices[0].union_many(all_indices[1:])

        aligned_frames = []
        for tf, df in data_dict.items():
            if df.empty:
                continue
            renamed = df.reindex(union_index).rename(
                columns={col: f"{col}_{tf}" for col in df.columns}
            )
            aligned_frames.append(renamed)

        if not aligned_frames:
            return pd.DataFrame()

        combined = pd.concat(aligned_frames, axis=1)
        combined = combined.dropna(how="all")
        self.logger.info("Aligned data across %d timeframes", len(aligned_frames))
        return combined
