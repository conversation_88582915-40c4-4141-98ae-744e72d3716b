# Hybrid Trading Agent

Indian Market & Crypto Market Trading System using Google GenAI.

## Installation

Install the required Python packages using `pip`:

```bash
pip install -r requirements.txt
```

**Note:** This project uses the `google-generativeai` library. Make sure you have the correct version installed.

## Configuration

Create a `config.json` file at the project root and add your API keys and
settings. **Do not commit your real keys to version control.** A minimal
example looks like this:

```json
{
  "google_api_key": "YOUR_GEMINI_API_KEY",
  "database": {
    "db_path": "crypto_market/data/crypto_trading.db"
  }
}
```

## Running the dashboard

Launch the Streamlit interface:

```bash
streamlit run GUI/app.py
```

## Google GenAI Library Usage

This project uses the standard `google-generativeai` library with the following key features:
- Function calling for market data analysis
- Multi-modal chart analysis
- Tool integration for comprehensive trading insights

## Optional: Local Model with Ollama

If you have [O<PERSON>ma](https://ollama.ai) installed and running, the dashboard can
generate analysis using a local multimodal model. Pull a model such as
`Janus-Pro-7B` with:

```bash
ollama pull Janus-Pro-7B
```

Then choose **Local** as the model source in the app.
