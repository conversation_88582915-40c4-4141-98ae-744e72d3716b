#!/usr/bin/env python3
"""
Test the improved symbol detection and progress reporting
"""

import sys
import os

# Add the project root to the Python path
script_dir = os.path.dirname(__file__)
project_root = os.path.abspath(os.path.join(script_dir, '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def test_symbol_detection_logic():
    """Test the symbol detection logic with different scenarios"""
    print("🧪 Testing Symbol Detection Logic...")
    
    # Test scenarios
    test_cases = [
        {
            "chart_structure": {"detected_text": "NIFTY 50 INDEX"},
            "prompt_type": "indian",
            "expected_symbol": "^NSEI",
            "expected_method": "text_detection"
        },
        {
            "chart_structure": {"detected_text": "BANK NIFTY"},
            "prompt_type": "indian", 
            "expected_symbol": "^NSEBANK",
            "expected_method": "text_detection"
        },
        {
            "chart_structure": {"detected_text": "BTC/USDT"},
            "prompt_type": "crypto",
            "expected_symbol": "BTCUSDT",
            "expected_method": "text_detection"
        },
        {
            "chart_structure": {"detected_text": ""},  # Empty text
            "prompt_type": "crypto",
            "expected_symbol": "BTCUSDT",
            "expected_method": "crypto_fallback"
        },
        {
            "chart_structure": None,  # No chart structure
            "prompt_type": "indian",
            "expected_symbol": "^NSEI",
            "expected_method": "indian_fallback"
        },
        {
            "chart_structure": {"detected_text": ""},  # Empty text
            "prompt_type": "positional",
            "expected_symbol": "^NSEI",
            "expected_method": "positional_fallback"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test Case {i}:")
        print(f"   Chart Structure: {test_case['chart_structure']}")
        print(f"   Prompt Type: {test_case['prompt_type']}")
        
        # Simulate the symbol detection logic
        detected_symbol = None
        symbol_detection_method = "none"
        chart_structure = test_case['chart_structure']
        prompt_type = test_case['prompt_type']
        
        # Try to extract symbol from chart text
        if chart_structure and chart_structure.get('detected_text'):
            text = chart_structure['detected_text'].upper()
            if 'NIFTY' in text and 'BANK' in text:
                detected_symbol = '^NSEBANK'
                symbol_detection_method = "text_detection"
            elif 'NIFTY' in text:
                detected_symbol = '^NSEI'
                symbol_detection_method = "text_detection"
            elif 'BTC' in text:
                detected_symbol = 'BTCUSDT'
                symbol_detection_method = "text_detection"
            elif 'ETH' in text:
                detected_symbol = 'ETHUSDT'
                symbol_detection_method = "text_detection"
            elif 'SOL' in text:
                detected_symbol = 'SOLUSDT'
                symbol_detection_method = "text_detection"
        
        # Fallback symbol detection based on analysis type
        if not detected_symbol:
            if prompt_type == "crypto":
                detected_symbol = 'BTCUSDT'  # Default crypto symbol
                symbol_detection_method = "crypto_fallback"
            elif prompt_type == "indian":
                detected_symbol = '^NSEI'  # Default Indian market symbol
                symbol_detection_method = "indian_fallback"
            else:
                detected_symbol = '^NSEI'  # Default positional symbol
                symbol_detection_method = "positional_fallback"
        
        # Check results
        if detected_symbol == test_case['expected_symbol'] and symbol_detection_method == test_case['expected_method']:
            print(f"   ✅ PASS: {detected_symbol} ({symbol_detection_method})")
        else:
            print(f"   ❌ FAIL: Expected {test_case['expected_symbol']} ({test_case['expected_method']})")
            print(f"           Got {detected_symbol} ({symbol_detection_method})")
            return False
    
    print(f"\n🎉 All symbol detection tests passed!")
    return True

def test_progress_callback():
    """Test the progress callback functionality"""
    print("\n🔄 Testing Progress Callback...")
    
    progress_messages = []
    
    def mock_progress_callback(message, step=None, total=None):
        progress_messages.append({
            'message': message,
            'step': step,
            'total': total
        })
        print(f"   📊 Progress: {message} ({step}/{total})")
    
    # Simulate progress updates
    mock_progress_callback("🚀 Starting optimized analysis...", 1, 6)
    mock_progress_callback("📋 Using positional analysis prompt", 2, 6)
    mock_progress_callback("⚡ Executing crypto tools for BTCUSDT...", 3, 6)
    mock_progress_callback("✅ Executed 3 tools successfully", 3, 6)
    mock_progress_callback("🤖 Initializing gemini-2.0-flash-exp...", 4, 6)
    mock_progress_callback("📊 Preparing analysis with 3 tool results...", 5, 6)
    mock_progress_callback("🧠 Generating AI analysis (2 images, 3 tools)...", 6, 6)
    mock_progress_callback("✅ Analysis completed successfully!", 6, 6)
    
    if len(progress_messages) == 8:
        print("✅ Progress callback test passed!")
        return True
    else:
        print(f"❌ Progress callback test failed: Expected 8 messages, got {len(progress_messages)}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Symbol Detection and Progress Improvements\n")
    
    tests = [
        test_symbol_detection_logic,
        test_progress_callback
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Symbol detection and progress improvements are working correctly.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
