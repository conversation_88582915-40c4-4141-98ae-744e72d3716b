# Copilot Instructions for Agent_Trading

## Overview

This repository contains advanced Pine Script (TradingView) strategies for algorithmic trading, with a focus on BTC. The main script (`BTC_ORIGINAL.txt`) implements a multi-layered signal engine, adaptive filters, and visual debugging tools. The codebase is optimized for performance and clarity, using modular helper functions and stateful logic.

## Architecture & Major Components

- **Signal Engine**: Computes buy/sell signals using a combination of trend, momentum, volume, and market structure filters. Scoring and confluence logic are central.
- **Adaptive Filters**: Includes volatility, choppiness, exhaustion, and higher-timeframe (HTF) filters. Many filters are dynamically parameterized based on market regime.
- **Market Structure**: Implements a pivot-based regime detector with adaptive swing sizing (ATR-scaled), supporting override logic for breakouts.
- **State Management**: Uses Pine Script `var` and barstate logic to track signal state, cooldowns, and trailing stop status.
- **Visual Debugging**: Extensive use of `plot`, `plotshape`, `label`, and `table` for real-time diagnostics and signal transparency.

## Key Patterns & Conventions

- **Helper Functions**: All repeated calculations (e.g., ADX, SuperTrend, MACD, RSI) are wrapped in local functions for clarity and DRYness.
- **Single Security Call per Timeframe**: Each timeframe's indicators are fetched in a single `request.security()` call, returning tuples for efficiency.
- **Adaptive Parameters**: Many thresholds (e.g., RSI, ATR period, swing size) are dynamically adjusted based on volatility regime or user input.
- **Stateful Signal Confirmation**: Signals are confirmed and locked using `var` variables and barstate checks to avoid repainting and ensure correct arrow/label timing.
- **Debug Table**: The script uses a `table` to display the current state of all major gates and filters, with color-coding for quick diagnostics.

## Developer Workflow

- **Edit Pine Script in `.txt` Files**: Main strategy logic is in `.txt` files (e.g., `BTC_ORIGINAL.txt`). Edit these directly and copy/paste into TradingView's Pine Script editor for testing.
- **No Build/Test Automation**: There are no build scripts or automated tests; all validation is done interactively in TradingView.
- **Versioning**: Strategy versions are indicated in the indicator title and comments (e.g., `v2.3 - OPTIMIZED`).
- **Debugging**: Use the on-chart debug table and visual overlays to diagnose logic errors or filter behavior.

## Project-Specific Guidance

- **Do not hard-code swing sizes or filter thresholds**; always use the adaptive formulas provided (see comments in code).
- **When adding new filters or gates**, ensure they are reflected in both the signal logic and the debug table.
- **Arrow/Label Timing**: Always plot arrows/labels based on the *current bar's* final signal, not the previous bar, to avoid misleading signals.
- **Override Logic**: When implementing breakout overrides, ensure the override flag is OR'ed into the relevant gate and reflected in the debug table's color logic.

## Key Files

- `BTC_ORIGINAL.txt`: Main Pine Script strategy, contains all logic, helper functions, and visualizations.
- `.github/copilot-instructions.md`: (this file) Guidance for AI agents and contributors.

## Example: Adaptive Pivot Swing

```pinescript
volPct  = ta.atr(100)/close*100
swingSize = round(baseSwing * clamp(volPct / volPctRef, 0.5, 1.5))
v1_pivot_high_val = ta.pivothigh(high, swingSize, swingSize)
```

## Example: Breakout Override

```pinescript
bullBreak = close > ta.highest(high, swingSize)[1] + atrNow * atrMultiple
bearBreak = close < ta.lowest (low , swingSize)[1] - atrNow * atrMultiple
ranging_override_breakout := bullBreak or bearBreak
ranging_market_filter_passed := v1_market_structure != "RANGING" or ranging_override_breakout
```

---

If any section is unclear or missing, please provide feedback for further refinement.
