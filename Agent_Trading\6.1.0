Collecting duckduckgo-search
  Using cached duckduckgo_search-8.1.1-py3-none-any.whl.metadata (16 kB)
Collecting click>=8.1.8 (from duckduckgo-search)
  Using cached click-8.2.1-py3-none-any.whl.metadata (2.5 kB)
Collecting primp>=0.15.0 (from duckduckgo-search)
  Using cached primp-0.15.0-cp38-abi3-win_amd64.whl.metadata (13 kB)
Collecting lxml>=5.3.0 (from duckduckgo-search)
  Using cached lxml-6.0.0-cp312-cp312-win_amd64.whl.metadata (6.8 kB)
Requirement already satisfied: colorama in c:\users\<USER>\anaconda3\lib\site-packages (from click>=8.1.8->duckduckgo-search) (0.4.6)
Using cached duckduckgo_search-8.1.1-py3-none-any.whl (18 kB)
Using cached click-8.2.1-py3-none-any.whl (102 kB)
Using cached lxml-6.0.0-cp312-cp312-win_amd64.whl (4.0 MB)
Using cached primp-0.15.0-cp38-abi3-win_amd64.whl (3.1 MB)
Installing collected packages: primp, lxml, click, duckduckgo-search
  Attempting uninstall: lxml
    Found existing installation: lxml 5.2.1
    Uninstalling lxml-5.2.1:
      Successfully uninstalled lxml-5.2.1
  Attempting uninstall: click
    Found existing installation: click 8.1.7
    Uninstalling click-8.1.7:
      Successfully uninstalled click-8.1.7

Successfully installed click-8.2.1 duckduckgo-search-8.1.1 lxml-6.0.0 primp-0.15.0
