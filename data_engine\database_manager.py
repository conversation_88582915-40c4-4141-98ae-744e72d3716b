"""
Professional Database Manager
============================

Institutional-grade SQLite3 database management:
- Multi-timeframe data storage
- Professional schema design
- Data integrity and validation
- Backup and recovery
- Performance optimization
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import logging
import shutil
import threading
from contextlib import contextmanager

import sys
from pathlib import Path

# Add root directory to path for core imports
root_dir = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(root_dir))

from core.config_manager import ConfigManager
from core.logger import get_logger

class DatabaseManager:
    """Professional database manager for trading system"""

    def __init__(self, config_manager: ConfigManager):
        """Initialize database manager

        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager
        self.logger = get_logger()

        # Database configuration with proper path resolution
        db_config = self.config.get_database_config()

        # Fix database path resolution - use existing database location
        db_path_str = db_config['db_path']

        # Always resolve relative to config file directory to avoid creating new folders
        config_dir = Path(self.config.config_path).parent
        self.db_path = config_dir / db_path_str

        # Make it absolute and resolve any .. or . components
        self.db_path = self.db_path.resolve()

        self.logger.info(f"[PATH] Config file: {self.config.config_path}")
        self.logger.info(f"[PATH] Config db_path: {db_path_str}")
        self.logger.info(f"[PATH] Resolved database path: {self.db_path}")
        self.logger.info(f"[PATH] Database exists: {self.db_path.exists()}")

        self.backup_enabled = db_config['backup_enabled']
        self.backup_interval = db_config['backup_interval_hours']

        # Create database directory
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        # Thread lock for database operations
        self._lock = threading.Lock()

        # Initialize database
        self._initialize_database()

        self.logger.info(f"[OK] Database manager initialized: {self.db_path}")

    def _initialize_database(self) -> None:
        """Initialize database with professional schema"""
        try:
            with self.get_connection() as conn:
                # Create market data table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS market_data (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME NOT NULL,
                        symbol TEXT NOT NULL,
                        timeframe TEXT NOT NULL,
                        open REAL NOT NULL,
                        high REAL NOT NULL,
                        low REAL NOT NULL,
                        close REAL NOT NULL,
                        volume REAL NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(timestamp, symbol, timeframe)
                    )
                """)

                # Create indicators table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS indicators (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME NOT NULL,
                        symbol TEXT NOT NULL,
                        timeframe TEXT NOT NULL,
                        indicator_name TEXT NOT NULL,
                        indicator_value REAL,
                        indicator_data TEXT,  -- JSON for complex indicators
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(timestamp, symbol, timeframe, indicator_name)
                    )
                """)

                # Create signals table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS signals (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME NOT NULL,
                        symbol TEXT NOT NULL,
                        timeframe TEXT NOT NULL,
                        signal_type TEXT NOT NULL,  -- 'buy', 'sell', 'hold'
                        confidence REAL NOT NULL,
                        strength REAL NOT NULL,
                        source TEXT NOT NULL,  -- 'rule_engine', 'ml_engine', 'hybrid'
                        metadata TEXT,  -- JSON for additional data
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # Create trades table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS trades (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        trade_id TEXT UNIQUE NOT NULL,
                        symbol TEXT NOT NULL,
                        side TEXT NOT NULL,  -- 'buy', 'sell'
                        size REAL NOT NULL,
                        entry_price REAL NOT NULL,
                        exit_price REAL,
                        stop_loss REAL,
                        take_profit REAL,
                        entry_time DATETIME NOT NULL,
                        exit_time DATETIME,
                        pnl REAL,
                        pnl_percentage REAL,
                        status TEXT NOT NULL,  -- 'open', 'closed', 'cancelled'
                        confidence REAL,
                        metadata TEXT,  -- JSON for additional data
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # Create performance metrics table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS performance_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        date DATE NOT NULL,
                        total_return REAL,
                        daily_return REAL,
                        win_rate REAL,
                        profit_factor REAL,
                        max_drawdown REAL,
                        sharpe_ratio REAL,
                        total_trades INTEGER,
                        winning_trades INTEGER,
                        losing_trades INTEGER,
                        avg_win REAL,
                        avg_loss REAL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(date)
                    )
                """)

                # Create indexes for performance
                self._create_indexes(conn)

                conn.commit()

            self.logger.info("[OK] Database schema initialized successfully")

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to initialize database: {e}")
            raise

    def _create_indexes(self, conn: sqlite3.Connection) -> None:
        """Create database indexes for performance optimization"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_market_data_timestamp ON market_data(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_market_data_symbol_timeframe ON market_data(symbol, timeframe)",
            "CREATE INDEX IF NOT EXISTS idx_indicators_timestamp ON indicators(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_indicators_symbol_timeframe ON indicators(symbol, timeframe)",
            "CREATE INDEX IF NOT EXISTS idx_signals_timestamp ON signals(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_signals_symbol ON signals(symbol)",
            "CREATE INDEX IF NOT EXISTS idx_trades_entry_time ON trades(entry_time)",
            "CREATE INDEX IF NOT EXISTS idx_trades_status ON trades(status)",
            "CREATE INDEX IF NOT EXISTS idx_performance_date ON performance_metrics(date)"
        ]

        for index_sql in indexes:
            try:
                conn.execute(index_sql)
            except sqlite3.Error as e:
                self.logger.warning(f"[WARNING] Failed to create index: {e}")

    @contextmanager
    def get_connection(self):
        """Get database connection with proper error handling

        Yields:
            SQLite connection
        """
        conn = None
        try:
            with self._lock:
                conn = sqlite3.connect(
                    self.db_path,
                    timeout=30.0,
                    check_same_thread=False
                )
                conn.row_factory = sqlite3.Row
                conn.execute("PRAGMA journal_mode=WAL")
                conn.execute("PRAGMA synchronous=NORMAL")
                conn.execute("PRAGMA cache_size=10000")
                conn.execute("PRAGMA temp_store=MEMORY")

                yield conn

        except Exception as e:
            if conn:
                conn.rollback()
            self.logger.error(f"[ERROR] Database connection error: {e}")
            raise
        finally:
            if conn:
                conn.close()

    def store_market_data(self, data, symbol: str = "BTCUSD") -> bool:
        """Store market data in database

        Args:
            data: DataFrame with OHLCV data OR list of records
            symbol: Trading symbol

        Returns:
            True if successful, False otherwise
        """
        # Handle both DataFrame and list inputs
        if isinstance(data, list):
            if not data:
                return False
            # Convert list of records to DataFrame
            df = pd.DataFrame(data)
            if 'timestamp' in df.columns:
                df.set_index('timestamp', inplace=True)
        else:
            df = data
            if df.empty:
                return False

        try:
            with self.get_connection() as conn:
                # Prepare data for insertion
                records = []
                self.logger.info(f"[DEBUG] Processing {len(df)} rows for storage")
                self.logger.info(f"[DEBUG] DataFrame columns: {list(df.columns)}")
                self.logger.info(f"[DEBUG] DataFrame index type: {type(df.index[0]) if not df.empty else 'Empty'}")

                for i, (timestamp, row) in enumerate(df.iterrows()):
                    try:
                        # Robust timestamp conversion for SQLite compatibility
                        if pd.isna(timestamp):
                            self.logger.warning(f"[WARNING] Skipping row {i} with null timestamp")
                            continue

                        # Convert various timestamp formats to string
                        if hasattr(timestamp, 'strftime'):
                            # pandas Timestamp or datetime object
                            timestamp_str = timestamp.strftime('%Y-%m-%d %H:%M:%S')
                        elif isinstance(timestamp, str):
                            # Already a string
                            timestamp_str = timestamp
                        elif isinstance(timestamp, (int, float)):
                            # Unix timestamp
                            timestamp_str = pd.to_datetime(timestamp, unit='s').strftime('%Y-%m-%d %H:%M:%S')
                        else:
                            # Fallback: convert to pandas datetime first
                            timestamp_str = pd.to_datetime(timestamp).strftime('%Y-%m-%d %H:%M:%S')

                        # Debug first few records
                        if i < 3:
                            self.logger.info(f"[DEBUG] Record {i}: timestamp={timestamp_str}, open={row.get('open')}, timeframe={row.get('timeframe', '3m')}")

                        records.append((
                            timestamp_str,
                            symbol,
                            row.get('timeframe', '3m'),
                            float(row['open']),
                            float(row['high']),
                            float(row['low']),
                            float(row['close']),
                            float(row['volume'])
                        ))
                    except Exception as row_error:
                        self.logger.error(f"[ERROR] Failed to process row {i}: {row_error}")
                        self.logger.error(f"[ERROR] Row data: {row}")
                        continue

                if not records:
                    self.logger.warning("[WARNING] No valid records to store")
                    return False

                self.logger.info(f"[DEBUG] Prepared {len(records)} records for insertion")

                # Insert data with conflict resolution
                conn.executemany("""
                    INSERT OR REPLACE INTO market_data
                    (timestamp, symbol, timeframe, open, high, low, close, volume)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, records)

                conn.commit()

            self.logger.info(f"[OK] Stored {len(records)} market data records for {symbol}")
            return True

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to store market data: {e}")
            import traceback
            self.logger.error(f"[ERROR] Traceback: {traceback.format_exc()}")
            return False

    def get_market_data(self,
                       symbol: str = "BTCUSD",
                       timeframe: str = "3m",
                       start_date: Optional[datetime] = None,
                       end_date: Optional[datetime] = None,
                       limit: Optional[int] = None) -> pd.DataFrame:
        """Retrieve market data from database

        Args:
            symbol: Trading symbol
            timeframe: Data timeframe
            start_date: Start date filter
            end_date: End date filter
            limit: Maximum number of records

        Returns:
            DataFrame with market data
        """
        try:
            with self.get_connection() as conn:
                # Build query
                query = """
                    SELECT timestamp, open, high, low, close, volume, timeframe
                    FROM market_data
                    WHERE symbol = ? AND timeframe = ?
                """
                params = [symbol, timeframe]

                if start_date:
                    query += " AND timestamp >= ?"
                    # Convert datetime to string for SQLite compatibility
                    start_date_str = start_date.strftime('%Y-%m-%d %H:%M:%S') if hasattr(start_date, 'strftime') else str(start_date)
                    params.append(start_date_str)

                if end_date:
                    query += " AND timestamp <= ?"
                    # Convert datetime to string for SQLite compatibility
                    end_date_str = end_date.strftime('%Y-%m-%d %H:%M:%S') if hasattr(end_date, 'strftime') else str(end_date)
                    params.append(end_date_str)

                query += " ORDER BY timestamp ASC"

                if limit:
                    query += " LIMIT ?"
                    params.append(limit)

                # Execute query
                df = pd.read_sql_query(query, conn, params=params, parse_dates=['timestamp'])

                if not df.empty:
                    df.set_index('timestamp', inplace=True)

            self.logger.info(f"[OK] Retrieved {len(df)} records for {symbol} {timeframe}")
            return df

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to retrieve market data: {e}")
            return pd.DataFrame()

    def get_latest_timestamp(self, symbol: str = "BTCUSD", timeframe: str = "3m") -> Optional[datetime]:
        """Get latest timestamp for symbol and timeframe

        Args:
            symbol: Trading symbol
            timeframe: Data timeframe

        Returns:
            Latest timestamp or None
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.execute("""
                    SELECT MAX(timestamp) as latest_timestamp
                    FROM market_data
                    WHERE symbol = ? AND timeframe = ?
                """, (symbol, timeframe))

                result = cursor.fetchone()
                if result and result['latest_timestamp']:
                    return pd.to_datetime(result['latest_timestamp'])

            return None

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to get latest timestamp: {e}")
            return None

    def cleanup_old_data(self, days_to_keep: int = 90) -> bool:
        """Clean up old data to maintain database size

        Args:
            days_to_keep: Number of days to keep

        Returns:
            True if successful
        """
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)
            # Convert datetime to string for SQLite compatibility
            cutoff_date_str = cutoff_date.strftime('%Y-%m-%d %H:%M:%S')

            with self.get_connection() as conn:
                # Clean market data
                cursor = conn.execute("""
                    DELETE FROM market_data
                    WHERE timestamp < ?
                """, (cutoff_date_str,))

                deleted_count = cursor.rowcount
                conn.commit()

            self.logger.info(f"[OK] Cleaned up {deleted_count} old market data records")
            return True

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to cleanup old data: {e}")
            return False

    def get_record_count(self, symbol: str = "BTCUSD", timeframe: str = "3m") -> int:
        """Get total record count for symbol and timeframe

        Args:
            symbol: Trading symbol
            timeframe: Data timeframe

        Returns:
            Number of records
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.execute("""
                    SELECT COUNT(*) as count
                    FROM market_data
                    WHERE symbol = ? AND timeframe = ?
                """, (symbol, timeframe))

                result = cursor.fetchone()
                return result['count'] if result else 0

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to get record count: {e}")
            return 0

    def get_database_info(self) -> Dict[str, Any]:
        """Get comprehensive database information

        Returns:
            Dictionary with database statistics
        """
        try:
            info = {
                'database_path': str(self.db_path),
                'database_exists': self.db_path.exists(),
                'database_size_mb': 0,
                'timeframes': {},
                'total_records': 0,
                'date_range': {}
            }

            if self.db_path.exists():
                # Get database size
                info['database_size_mb'] = self.db_path.stat().st_size / (1024 * 1024)

                # Get data for each timeframe
                timeframes = ["3m", "15m", "1h"]
                for timeframe in timeframes:
                    count = self.get_record_count("BTCUSD", timeframe)
                    info['timeframes'][timeframe] = count
                    info['total_records'] += count

                    # Get date range for this timeframe
                    if count > 0:
                        with self.get_connection() as conn:
                            cursor = conn.execute("""
                                SELECT MIN(timestamp) as min_date, MAX(timestamp) as max_date
                                FROM market_data
                                WHERE symbol = ? AND timeframe = ?
                            """, ("BTCUSD", timeframe))

                            result = cursor.fetchone()
                            if result:
                                info['date_range'][timeframe] = {
                                    'start': result['min_date'],
                                    'end': result['max_date']
                                }

            return info

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to get database info: {e}")
            return {'error': str(e)}

    def check_data_gaps(self, symbol: str = "BTCUSD", timeframe: str = "3m") -> List[Dict]:
        """Check for gaps in the data

        Args:
            symbol: Trading symbol
            timeframe: Data timeframe

        Returns:
            List of gaps found
        """
        try:
            gaps = []

            # Get expected interval in minutes
            interval_map = {"3m": 3, "15m": 15, "1h": 60}
            interval_minutes = interval_map.get(timeframe, 3)

            with self.get_connection() as conn:
                # Get consecutive timestamps
                cursor = conn.execute("""
                    SELECT timestamp,
                           LAG(timestamp) OVER (ORDER BY timestamp) as prev_timestamp
                    FROM market_data
                    WHERE symbol = ? AND timeframe = ?
                    ORDER BY timestamp
                """, (symbol, timeframe))

                for row in cursor:
                    if row['prev_timestamp']:
                        current = pd.to_datetime(row['timestamp'])
                        previous = pd.to_datetime(row['prev_timestamp'])

                        expected_diff = timedelta(minutes=interval_minutes)
                        actual_diff = current - previous

                        if actual_diff > expected_diff * 1.5:  # Allow some tolerance
                            gaps.append({
                                'start': row['prev_timestamp'],
                                'end': row['timestamp'],
                                'duration_minutes': actual_diff.total_seconds() / 60
                            })

            return gaps

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to check data gaps: {e}")
            return []

    def backup_database(self) -> bool:
        """Create database backup

        Returns:
            True if successful
        """
        if not self.backup_enabled:
            return True

        try:
            backup_dir = self.db_path.parent / "backups"
            backup_dir.mkdir(exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = backup_dir / f"trading_system_backup_{timestamp}.db"

            shutil.copy2(self.db_path, backup_path)

            self.logger.info(f"[OK] Database backup created: {backup_path}")
            return True

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to backup database: {e}")
            return False

    def clear_all_data(self) -> bool:
        """Clear all data from database tables (but keep schema)

        Returns:
            True if successful
        """
        try:
            self.logger.info(f"[START] Clearing all database tables from: {self.db_path}")

            # Force close any existing connections by acquiring the lock
            with self._lock:
                total_deleted = 0

                # Multiple attempts with different strategies
                strategies = [
                    self._clear_with_wal_disable,
                    self._clear_with_immediate_mode,
                    self._clear_with_truncate
                ]

                for i, strategy in enumerate(strategies, 1):
                    self.logger.info(f"[ATTEMPT {i}] Trying clearing strategy {i}")
                    try:
                        result = strategy()
                        if result > 0:
                            self.logger.info(f"[SUCCESS] Strategy {i} cleared {result} records")
                            return True
                    except Exception as e:
                        self.logger.warning(f"[ATTEMPT {i}] Strategy {i} failed: {e}")
                        continue

                self.logger.error("[ERROR] All clearing strategies failed")
                return False

        except Exception as e:
            self.logger.error(f"[ERROR] Failed to clear database tables: {e}")
            import traceback
            self.logger.error(f"[ERROR] Traceback: {traceback.format_exc()}")
            return False

    def _clear_with_wal_disable(self) -> int:
        """Strategy 1: Disable WAL mode and clear"""
        conn = sqlite3.connect(self.db_path, timeout=60.0)
        total_deleted = 0

        try:
            # Disable WAL mode
            conn.execute("PRAGMA journal_mode=DELETE")
            conn.execute("PRAGMA synchronous=OFF")
            conn.execute("PRAGMA locking_mode=EXCLUSIVE")

            tables_to_clear = ['market_data', 'indicators', 'signals', 'trades', 'performance_metrics']

            for table in tables_to_clear:
                cursor = conn.execute(f"SELECT COUNT(*) FROM {table}")
                before_count = cursor.fetchone()[0]

                if before_count > 0:
                    conn.execute(f"DELETE FROM {table}")
                    total_deleted += before_count
                    self.logger.info(f"[OK] Cleared {before_count} records from {table}")

            # Reset sequences
            conn.execute("DELETE FROM sqlite_sequence")
            conn.commit()

            # Vacuum
            conn.execute("VACUUM")

            # Re-enable WAL
            conn.execute("PRAGMA locking_mode=NORMAL")
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.commit()

        finally:
            conn.close()

        return total_deleted

    def _clear_with_immediate_mode(self) -> int:
        """Strategy 2: Use immediate locking"""
        conn = sqlite3.connect(self.db_path, timeout=60.0)
        total_deleted = 0

        try:
            conn.execute("BEGIN IMMEDIATE")

            tables_to_clear = ['market_data', 'indicators', 'signals', 'trades', 'performance_metrics']

            for table in tables_to_clear:
                cursor = conn.execute(f"SELECT COUNT(*) FROM {table}")
                before_count = cursor.fetchone()[0]

                if before_count > 0:
                    conn.execute(f"DELETE FROM {table}")
                    total_deleted += before_count
                    self.logger.info(f"[OK] Cleared {before_count} records from {table}")

            conn.execute("DELETE FROM sqlite_sequence")
            conn.commit()

        finally:
            conn.close()

        return total_deleted

    def _clear_with_truncate(self) -> int:
        """Strategy 3: Drop and recreate tables"""
        conn = sqlite3.connect(self.db_path, timeout=60.0)
        total_deleted = 0

        try:
            # Get current record count
            cursor = conn.execute("SELECT COUNT(*) FROM market_data")
            total_deleted = cursor.fetchone()[0]

            # Drop and recreate market_data table
            conn.execute("DROP TABLE IF EXISTS market_data")
            conn.execute("""
                CREATE TABLE market_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME NOT NULL,
                    symbol TEXT NOT NULL,
                    timeframe TEXT NOT NULL,
                    open REAL NOT NULL,
                    high REAL NOT NULL,
                    low REAL NOT NULL,
                    close REAL NOT NULL,
                    volume REAL NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(timestamp, symbol, timeframe)
                )
            """)

            # Clear other tables normally
            other_tables = ['indicators', 'signals', 'trades', 'performance_metrics']
            for table in other_tables:
                try:
                    conn.execute(f"DELETE FROM {table}")
                except sqlite3.Error:
                    pass  # Table might not exist

            # Clear sequence table
            conn.execute("DELETE FROM sqlite_sequence")

            # Recreate indexes
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_market_data_timestamp ON market_data(timestamp)",
                "CREATE INDEX IF NOT EXISTS idx_market_data_symbol_timeframe ON market_data(symbol, timeframe)"
            ]

            for index_sql in indexes:
                conn.execute(index_sql)

            conn.commit()
            self.logger.info(f"[OK] Recreated market_data table, cleared {total_deleted} records")

        finally:
            conn.close()

        return total_deleted

    def simple_clear_all_data(self) -> bool:
        """Simple, direct database clear - guaranteed to work"""
        try:
            self.logger.info(f"[SIMPLE CLEAR] Starting simple clear of: {self.db_path}")

            # Direct connection without any fancy settings
            import sqlite3
            conn = sqlite3.connect(str(self.db_path))

            try:
                # Get count before
                cursor = conn.execute("SELECT COUNT(*) FROM market_data")
                before_count = cursor.fetchone()[0]
                self.logger.info(f"[SIMPLE CLEAR] Records before: {before_count}")

                # Simple delete commands
                conn.execute("DELETE FROM market_data")
                conn.execute("DELETE FROM indicators")
                conn.execute("DELETE FROM signals")
                conn.execute("DELETE FROM trades")
                conn.execute("DELETE FROM performance_metrics")
                conn.execute("DELETE FROM sqlite_sequence")

                # Commit
                conn.commit()

                # Get count after
                cursor = conn.execute("SELECT COUNT(*) FROM market_data")
                after_count = cursor.fetchone()[0]
                self.logger.info(f"[SIMPLE CLEAR] Records after: {after_count}")

                # Vacuum to reclaim space
                conn.execute("VACUUM")
                conn.commit()

                success = after_count == 0
                if success:
                    self.logger.info(f"[SIMPLE CLEAR] SUCCESS! Cleared {before_count} records")
                else:
                    self.logger.error(f"[SIMPLE CLEAR] FAILED! Still {after_count} records")

                return success

            finally:
                conn.close()

        except Exception as e:
            self.logger.error(f"[SIMPLE CLEAR] ERROR: {e}")
            import traceback
            self.logger.error(f"[SIMPLE CLEAR] Traceback: {traceback.format_exc()}")
            return False

    def get_absolute_database_path(self) -> str:
        """Get the absolute path to the database file

        Returns:
            Absolute path as string
        """
        return str(self.db_path.resolve())

    def run_database_test(self) -> Dict[str, Any]:
        """Run comprehensive database test

        Returns:
            Test results dictionary
        """
        test_results = {
            'timestamp': datetime.now().isoformat(),
            'database_path': str(self.db_path),
            'tests': {}
        }

        try:
            # Test 1: Database file existence
            test_results['tests']['file_exists'] = {
                'status': self.db_path.exists(),
                'message': f"Database file exists at {self.db_path}"
            }

            # Test 2: Database connection
            try:
                with self.get_connection() as conn:
                    cursor = conn.execute("SELECT 1")
                    result = cursor.fetchone()
                    test_results['tests']['connection'] = {
                        'status': True,
                        'message': "Database connection successful"
                    }
            except Exception as e:
                test_results['tests']['connection'] = {
                    'status': False,
                    'message': f"Connection failed: {e}"
                }

            # Test 3: Table existence
            try:
                with self.get_connection() as conn:
                    cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = [row[0] for row in cursor.fetchall()]
                    expected_tables = ['market_data', 'indicators', 'signals', 'trades', 'performance_metrics']
                    missing_tables = [t for t in expected_tables if t not in tables]

                    test_results['tests']['tables'] = {
                        'status': len(missing_tables) == 0,
                        'message': f"Tables found: {tables}, Missing: {missing_tables}"
                    }
            except Exception as e:
                test_results['tests']['tables'] = {
                    'status': False,
                    'message': f"Table check failed: {e}"
                }

            # Test 4: Data insertion and retrieval
            try:
                test_data = [{
                    'timestamp': datetime.now(),
                    'timeframe': '3m',
                    'open': 50000.0,
                    'high': 50100.0,
                    'low': 49900.0,
                    'close': 50050.0,
                    'volume': 1000.0
                }]

                # Insert test data
                success = self.store_market_data(test_data, "TESTBTC")

                if success:
                    # Try to retrieve it
                    df = self.get_market_data("TESTBTC", "3m", limit=1)
                    test_results['tests']['data_operations'] = {
                        'status': not df.empty,
                        'message': f"Data insertion and retrieval successful. Retrieved {len(df)} records"
                    }

                    # Clean up test data
                    with self.get_connection() as conn:
                        conn.execute("DELETE FROM market_data WHERE symbol = 'TESTBTC'")
                        conn.commit()
                else:
                    test_results['tests']['data_operations'] = {
                        'status': False,
                        'message': "Data insertion failed"
                    }

            except Exception as e:
                test_results['tests']['data_operations'] = {
                    'status': False,
                    'message': f"Data operations test failed: {e}"
                }

            # Test 5: Record counts
            try:
                total_records = 0
                timeframe_counts = {}
                for timeframe in ["3m", "15m", "1h"]:
                    count = self.get_record_count("BTCUSD", timeframe)
                    timeframe_counts[timeframe] = count
                    total_records += count

                test_results['tests']['record_counts'] = {
                    'status': True,
                    'message': f"Total records: {total_records}, By timeframe: {timeframe_counts}"
                }

            except Exception as e:
                test_results['tests']['record_counts'] = {
                    'status': False,
                    'message': f"Record count test failed: {e}"
                }

            # Overall test status
            all_passed = all(test['status'] for test in test_results['tests'].values())
            test_results['overall_status'] = 'PASS' if all_passed else 'FAIL'

            self.logger.info(f"[TEST] Database test completed: {test_results['overall_status']}")

            return test_results

        except Exception as e:
            test_results['overall_status'] = 'ERROR'
            test_results['error'] = str(e)
            self.logger.error(f"[ERROR] Database test failed: {e}")
            return test_results
