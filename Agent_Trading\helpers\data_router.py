"""
Smart Data Source Router
Automatically routes data requests to the appropriate source based on symbol type.
"""

import re
import yfinance as yf
import warnings
from typing import Dict, Any, Optional, List
from .data_tools import fetch_market_data, fetch_comprehensive_market_data
from .delta_exchange import get_crypto_market_data
from .dhan_api import get_dhan_market_data, is_dhan_available
from .enhanced_news_sources import enhanced_news_extractor
from .indian_market_tools import indian_market_analyzer

class DataSourceRouter:
    """Routes data requests to appropriate sources based on symbol patterns."""

    def __init__(self):
        # Define major constituents for Indian indices
        self.nifty50_major_stocks = [
            "RELIANCE.NS", "TCS.NS", "HDFCBANK.NS", "INFY.NS", "HINDUNILVR.NS",
            "ICICIBANK.NS", "KOTAKBANK.NS", "BHARTIARTL.NS", "ITC.NS", "SBIN.NS",
            "BAJFINANCE.NS", "LICI.NS", "HCLTECH.NS", "ASIANPAINT.NS", "MARUTI.NS"
        ]

        self.banknifty_major_stocks = [
            "HDFCBANK.NS", "ICICIBANK.NS", "KOTAKBANK.NS", "SBIN.NS", "AXISBANK.NS",
            "INDUSINDBK.NS", "BAJFINANCE.NS", "BANDHANBNK.NS", "FEDERALBNK.NS", "IDFCFIRSTB.NS"
        ]

        # Global economic indicators that affect Indian markets
        self.global_indicators = [
            "^GSPC",  # S&P 500
            "^IXIC",  # NASDAQ
            "^DJI",   # Dow Jones
            "^VIX",   # VIX (Fear index)
            "GC=F",   # Gold futures
            "CL=F",   # Crude oil futures
            "^TNX",   # 10-year Treasury yield
            "DX-Y.NYB"  # US Dollar Index
        ]

        # Crypto market leaders that affect overall crypto sentiment
        self.crypto_leaders = [
            "BTC-USD", "ETH-USD", "BNB-USD", "XRP-USD", "ADA-USD"
        ]

        # Symbol conversion mappings
        self.crypto_symbol_map = {
            # Delta Exchange format -> yfinance format
            "BTCUSDT": "BTC-USD",
            "ETHUSDT": "ETH-USD",
            "SOLUSDT": "SOL-USD",
            "GOLDUSDT": "GC=F",  # Gold futures
            # Also handle reverse mapping
            "BTC-USD": "BTCUSDT",
            "ETH-USD": "ETHUSDT",
            "SOL-USD": "SOLUSDT",
            # Handle variations
            "BTCUSD": "BTC-USD",
            "ETHUSD": "ETH-USD",
            "SOLUSD": "SOL-USD",
            "BTC": "BTC-USD",
            "ETH": "ETH-USD",
            "SOL": "SOL-USD"
        }
        # Define symbol patterns for different data sources
        self.crypto_patterns = [
            r'BTC.*USDT?',
            r'ETH.*USDT?', 
            r'SOL.*USDT?',
            r'.*USDT$',
            r'BTC-USD',
            r'ETH-USD',
            r'SOL-USD'
        ]
        
        self.indian_market_patterns = [
            r'\^NSEI',      # Nifty 50
            r'\^NSEBANK',   # Bank Nifty
            r'.*\.NS$',     # NSE stocks
            r'.*\.BO$',     # BSE stocks
            r'NIFTY',
            r'BANKNIFTY'
        ]
        
        self.us_market_patterns = [
            r'^[A-Z]{1,5}$',  # US stock symbols
            r'.*-USD$',       # USD pairs
            r'\^.*',          # US indices (except Indian)
        ]

    def convert_symbol_for_yfinance(self, symbol: str) -> str:
        """Convert symbol to yfinance format."""
        symbol_upper = symbol.upper()

        # Direct mapping for crypto symbols to yfinance format
        crypto_to_yf = {
            "BTCUSDT": "BTC-USD",
            "ETHUSDT": "ETH-USD",
            "SOLUSDT": "SOL-USD",
            "GOLDUSDT": "GC=F",
            "BTCUSD": "BTC-USD",
            "ETHUSD": "ETH-USD",
            "SOLUSD": "SOL-USD",
            "BTC": "BTC-USD",
            "ETH": "ETH-USD",
            "SOL": "SOL-USD"
        }

        return crypto_to_yf.get(symbol_upper, symbol)

    def convert_symbol_for_delta(self, symbol: str) -> str:
        """Convert symbol to Delta Exchange format."""
        symbol_upper = symbol.upper()

        # Direct mapping for symbols to Delta Exchange format
        to_delta = {
            "BTC-USD": "BTCUSDT",
            "ETH-USD": "ETHUSDT",
            "SOL-USD": "SOLUSDT",
            "BTC": "BTCUSDT",
            "ETH": "ETHUSDT",
            "SOL": "SOLUSDT",
            "BTCUSD": "BTCUSDT",
            "ETHUSD": "ETHUSDT",
            "SOLUSD": "SOLUSDT"
        }

        return to_delta.get(symbol_upper, symbol)
    
    def identify_symbol_type(self, symbol: str) -> str:
        """Identify the type of symbol and appropriate data source."""
        symbol_upper = symbol.upper()
        
        # Check crypto patterns first
        for pattern in self.crypto_patterns:
            if re.match(pattern, symbol_upper):
                return "crypto"
        
        # Check Indian market patterns
        for pattern in self.indian_market_patterns:
            if re.match(pattern, symbol_upper):
                return "indian_market"
        
        # Check US market patterns
        for pattern in self.us_market_patterns:
            if re.match(pattern, symbol_upper):
                return "us_market"
        
        # Default to yfinance for unknown symbols
        return "yfinance_default"
    
    def get_market_data(self, symbol: str, period: str = "1mo", interval: str = "1d", 
                       comprehensive: bool = False, reason: str = "") -> Dict[str, Any]:
        """
        Route data request to appropriate source.
        
        Args:
            symbol: Trading symbol
            period: Time period
            interval: Data interval
            comprehensive: Whether to fetch comprehensive data (news, recommendations, etc.)
            reason: Why this data is needed
        """
        symbol_type = self.identify_symbol_type(symbol)
        
        try:
            if symbol_type == "crypto":
                # Convert symbol to appropriate format
                delta_symbol = self.convert_symbol_for_delta(symbol)
                yf_symbol = self.convert_symbol_for_yfinance(symbol)

                # Use Delta Exchange for main crypto pairs
                if delta_symbol in ["BTCUSDT", "ETHUSDT", "SOLUSDT"]:
                    return get_crypto_market_data(delta_symbol, period, interval)
                else:
                    # Fallback to yfinance for other crypto with proper symbol format
                    return self._fetch_yfinance_data(yf_symbol, period, interval, comprehensive, reason)
            
            elif symbol_type == "indian_market":
                # Use Dhan API (preferred) or yfinance (fallback) for Indian markets
                return self._fetch_indian_market_data(symbol, period, interval, comprehensive, reason)

            elif symbol_type in ["us_market", "yfinance_default"]:
                # Use yfinance for US markets and other symbols
                return self._fetch_yfinance_data(symbol, period, interval, comprehensive, reason)
            
            else:
                return {"error": f"Unknown symbol type for {symbol}"}
                
        except Exception as e:
            return {"error": f"Failed to fetch data for {symbol}: {str(e)}"}
    
    def _fetch_indian_market_data(self, symbol: str, period: str, interval: str,
                                comprehensive: bool, reason: str) -> Dict[str, Any]:
        """Fetch Indian market data using Dhan API (preferred) or yfinance (fallback)."""
        # Try Dhan API first for better Indian market data
        if is_dhan_available():
            try:
                dhan_result = get_dhan_market_data(symbol, period, interval, reason)
                if dhan_result.get("success", False):
                    # Add comprehensive data if requested
                    if comprehensive:
                        # Get news and additional context using yfinance
                        yf_data = fetch_comprehensive_market_data(symbol, period, interval, reason)
                        dhan_result.update({
                            "news": yf_data.get("news", []),
                            "recommendations": yf_data.get("recommendations", []),
                            "company_info": yf_data.get("company_info", {}),
                            "data_source": "Dhan API + yfinance (comprehensive)"
                        })
                    return dhan_result
            except Exception as e:
                # Fall back to yfinance if Dhan fails
                pass

        # Fallback to yfinance
        result = self._fetch_yfinance_data(symbol, period, interval, comprehensive, reason)
        result["data_source"] = result.get("data_source", "yfinance") + " (Dhan API not available)"
        return result

    def _fetch_yfinance_data(self, symbol: str, period: str, interval: str,
                           comprehensive: bool, reason: str) -> Dict[str, Any]:
        """Fetch data using yfinance with optional comprehensive data."""
        if comprehensive:
            return fetch_comprehensive_market_data(symbol, period, interval, reason)
        else:
            return fetch_market_data(symbol, period, interval, reason)
    
    def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """Get information about a symbol and its data source."""
        symbol_type = self.identify_symbol_type(symbol)
        
        info = {
            "symbol": symbol,
            "symbol_type": symbol_type,
            "data_source": self._get_data_source_name(symbol_type),
            "supported_features": self._get_supported_features(symbol_type)
        }
        
        return info
    
    def _get_data_source_name(self, symbol_type: str) -> str:
        """Get human-readable data source name."""
        mapping = {
            "crypto": "Delta Exchange API",
            "indian_market": "Dhan API + yfinance (Indian Markets)" if is_dhan_available() else "yfinance (Indian Markets - Dhan API not available)",
            "us_market": "yfinance (US Markets)",
            "yfinance_default": "yfinance (Default)"
        }
        return mapping.get(symbol_type, "Unknown")
    
    def _get_supported_features(self, symbol_type: str) -> list:
        """Get list of supported features for each data source."""
        if symbol_type == "crypto":
            return ["real_time_prices", "historical_data", "volume", "technical_indicators"]
        elif symbol_type in ["indian_market", "us_market", "yfinance_default"]:
            return ["real_time_prices", "historical_data", "news", "analyst_recommendations", 
                   "company_info", "financial_data", "volume"]
        else:
            return []

    def get_comprehensive_news(self, symbol: str, max_news: int = 20) -> Dict[str, Any]:
        """
        Get comprehensive news for a symbol including:
        - Direct symbol news (if available)
        - Constituent company news (for indices)
        - Global market news (for context)
        - Sector-specific news
        """
        symbol_type = self.identify_symbol_type(symbol)
        all_news = []
        news_sources = []

        try:
            # 1. Try to get direct news for the symbol
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                ticker = yf.Ticker(symbol)
                try:
                    direct_news = ticker.news
                    if direct_news:
                        # Fix news extraction for new yfinance structure
                        for item in direct_news[:5]:
                            content = item.get("content", {})
                            all_news.append({
                                "title": content.get("title", ""),
                                "publisher": content.get("provider", {}).get("displayName", ""),
                                "link": content.get("clickThroughUrl", {}).get("url", ""),
                                "providerPublishTime": content.get("pubDate", ""),
                                "summary": content.get("summary", ""),
                                "source_type": "direct",
                                "source_symbol": symbol
                            })
                        news_sources.append(f"Direct news for {symbol}")
                except:
                    pass

            # 2. Get constituent company news for indices
            if symbol.upper() in ["^NSEI", "NIFTY"]:
                constituent_news = self._get_constituent_news(self.nifty50_major_stocks[:5], "Nifty 50 constituents")
                all_news.extend(constituent_news)
                news_sources.append("Nifty 50 major constituents")

            elif symbol.upper() in ["^NSEBANK", "BANKNIFTY"]:
                constituent_news = self._get_constituent_news(self.banknifty_major_stocks[:5], "Bank Nifty constituents")
                all_news.extend(constituent_news)
                news_sources.append("Bank Nifty major constituents")

            # 3. Get global market context for Indian indices
            if symbol_type == "indian_market":
                global_news = self._get_global_market_news()
                all_news.extend(global_news)
                news_sources.append("Global market indicators")

            # 4. Get crypto market context for crypto symbols
            elif symbol_type == "crypto":
                crypto_news = self._get_crypto_market_news()
                all_news.extend(crypto_news)
                news_sources.append("Major crypto market leaders")

            # Sort by timestamp (most recent first) and limit
            all_news = sorted(all_news,
                            key=lambda x: x.get("providerPublishTime", 0),
                            reverse=True)[:max_news]

            return {
                "symbol": symbol,
                "symbol_type": symbol_type,
                "total_news_items": len(all_news),
                "news_sources": news_sources,
                "news": all_news,
                "success": True
            }

        except Exception as e:
            return {
                "symbol": symbol,
                "error": f"Failed to fetch comprehensive news: {str(e)}",
                "success": False
            }

    def _get_constituent_news(self, symbols: List[str], source_description: str) -> List[Dict]:
        """Get news from constituent companies."""
        constituent_news = []

        for symbol in symbols:
            try:
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    ticker = yf.Ticker(symbol)
                    news = ticker.news
                    if news:
                        # Take top 2 news items from each constituent
                        for item in news[:2]:
                            constituent_news.append({
                                "title": item.get("title", ""),
                                "publisher": item.get("publisher", ""),
                                "link": item.get("link", ""),
                                "providerPublishTime": item.get("providerPublishTime", ""),
                                "source_type": "constituent",
                                "source_symbol": symbol,
                                "source_description": source_description
                            })
            except:
                continue

        return constituent_news

    def _get_global_market_news(self) -> List[Dict]:
        """Get news from global market indicators that affect Indian markets."""
        global_news = []

        # Focus on most impactful global indicators
        key_indicators = ["^GSPC", "^VIX", "GC=F", "CL=F"]

        for symbol in key_indicators:
            try:
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    ticker = yf.Ticker(symbol)
                    news = ticker.news
                    if news:
                        # Take top 1 news item from each global indicator
                        item = news[0]
                        global_news.append({
                            "title": item.get("title", ""),
                            "publisher": item.get("publisher", ""),
                            "link": item.get("link", ""),
                            "providerPublishTime": item.get("providerPublishTime", ""),
                            "source_type": "global_indicator",
                            "source_symbol": symbol,
                            "source_description": f"Global market indicator ({symbol})"
                        })
            except:
                continue

        return global_news

    def _get_crypto_market_news(self) -> List[Dict]:
        """Get news from major crypto market leaders."""
        crypto_news = []

        # Focus on most impactful crypto assets
        key_cryptos = ["BTC-USD", "ETH-USD"]

        for symbol in key_cryptos:
            try:
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    ticker = yf.Ticker(symbol)
                    news = ticker.news
                    if news:
                        # Take top 2 news items from each major crypto
                        for item in news[:2]:
                            # Extract from nested content structure
                            content = item.get("content", {})
                            crypto_news.append({
                                "title": content.get("title", ""),
                                "publisher": content.get("provider", {}).get("displayName", ""),
                                "link": content.get("clickThroughUrl", {}).get("url", ""),
                                "providerPublishTime": content.get("pubDate", ""),
                                "summary": content.get("summary", ""),
                                "source_type": "crypto_leader",
                                "source_symbol": symbol,
                                "source_description": f"Major crypto asset ({symbol})"
                            })
            except:
                continue

        return crypto_news

# Global router instance
data_router = DataSourceRouter()

def get_smart_market_data(symbol: str, period: str = "1mo", interval: str = "1d", 
                         comprehensive: bool = False, reason: str = "") -> Dict[str, Any]:
    """
    Smart function that automatically routes to the best data source.
    
    Usage examples:
    - get_smart_market_data("BTCUSDT") -> Uses Delta Exchange
    - get_smart_market_data("^NSEI") -> Uses yfinance for Nifty 50
    - get_smart_market_data("AAPL", comprehensive=True) -> Uses yfinance with news/recommendations
    """
    return data_router.get_market_data(symbol, period, interval, comprehensive, reason)

def get_symbol_routing_info(symbol: str) -> Dict[str, Any]:
    """Get information about how a symbol will be routed."""
    return data_router.get_symbol_info(symbol)

def get_comprehensive_market_news(symbol: str = None, max_news: int = 20) -> Dict[str, Any]:
    """
    Get comprehensive news with enhanced Indian market coverage and better extraction.

    For indices like Nifty 50/Bank Nifty:
    - Gets news from major constituent companies
    - Uses enhanced Indian news sources (Economic Times, Moneycontrol, etc.)
    - Provides better sentiment analysis and theme extraction
    - Includes global market indicators that affect Indian markets

    For crypto:
    - Gets news from major crypto leaders (BTC, ETH)
    - Includes broader crypto market context

    For individual stocks:
    - Gets direct company news
    - Includes sector and market context
    """
    # Use enhanced news extraction for better coverage
    try:
        # Check if it's an Indian market symbol for specialized coverage
        is_indian = indian_market_analyzer.detect_symbol_from_text(symbol or "nifty") in ['^NSEI', '^NSEBANK', '^BSESN']

        if is_indian or symbol is None:
            # Use enhanced Indian market news extraction
            return enhanced_news_extractor.get_comprehensive_news(symbol, max_news)
        else:
            # Use standard data router for non-Indian symbols
            return data_router.get_comprehensive_news(symbol, max_news)
    except Exception as e:
        # Fallback to standard method
        if symbol is None:
            symbol = "^NSEI"  # Default to Nifty 50 for general market news
        return data_router.get_comprehensive_news(symbol, max_news)

def get_market_context_summary(symbol: str = None) -> Dict[str, Any]:
    """
    Get a comprehensive market context summary including:
    - Price data
    - News summary
    - Global market sentiment
    - Sector performance (for stocks)
    """
    try:
        # If no symbol provided, use general market context
        if symbol is None:
            symbol = "^NSEI"  # Default to Nifty 50 for general market context

        # Get basic market data
        market_data = data_router.get_market_data(symbol, "5d", "1d", comprehensive=False,
                                                 reason="Market context summary")

        # Get comprehensive news
        news_data = data_router.get_comprehensive_news(symbol, max_news=10)

        # Combine into summary with corrected data extraction
        context = {
            "symbol": symbol,
            "symbol_type": data_router.identify_symbol_type(symbol),
            "price_summary": {
                "current_price": market_data.get("current_price"),
                "price_change_24h": market_data.get("price_change_24h"),
                "latest_close": market_data.get("summary", {}).get("latest_close"),
                "highest_24h": market_data.get("summary", {}).get("highest"),
                "lowest_24h": market_data.get("summary", {}).get("lowest"),
                "avg_volume": market_data.get("summary", {}).get("avg_volume"),
                "data_source": "Delta Exchange" if symbol.upper().endswith("USDT") else "yfinance"
            },
            "news_summary": {
                "total_news_items": news_data.get("total_news_items", 0),
                "news_sources": news_data.get("news_sources", []),
                "latest_headlines": [item.get("title", "") for item in news_data.get("news", [])[:5] if item.get("title")],
                "news_available": len([item for item in news_data.get("news", []) if item.get("title")])
            },
            "market_factors": _get_market_factors(symbol),
            "success": True
        }

        return context

    except Exception as e:
        return {
            "symbol": symbol,
            "error": f"Failed to get market context: {str(e)}",
            "success": False
        }

def _get_market_factors(symbol: str) -> List[str]:
    """Get list of factors that typically affect this symbol."""
    symbol_type = data_router.identify_symbol_type(symbol)

    if symbol_type == "indian_market":
        if "BANK" in symbol.upper():
            return [
                "RBI monetary policy decisions",
                "Banking sector NPA levels",
                "Credit growth rates",
                "Government fiscal policies",
                "Global interest rate trends"
            ]
        else:  # General Indian market
            return [
                "FII/DII flows",
                "US Federal Reserve policies",
                "Crude oil prices",
                "INR/USD exchange rate",
                "Monsoon and agricultural output",
                "Government policy announcements"
            ]
    elif symbol_type == "crypto":
        return [
            "Bitcoin price movements",
            "Regulatory announcements",
            "Institutional adoption news",
            "DeFi protocol developments",
            "Global risk sentiment",
            "Stablecoin market stability"
        ]
    else:
        return [
            "Company earnings reports",
            "Sector-specific news",
            "Economic indicators",
            "Federal Reserve policies",
            "Geopolitical events"
        ]
