# Computer Vision Alternative for Python 3.13
# Install these packages as cv2 alternative

# Primary computer vision library (Python 3.13 compatible)
scikit-image>=0.21.0

# Supporting libraries for image processing
scipy>=1.11.0
numpy>=1.24.0

# Optional: Additional image processing capabilities
imageio>=2.31.0
matplotlib>=3.7.0

# Installation command:
# pip install scikit-image scipy numpy imageio matplotlib

# Note: These libraries provide similar functionality to OpenCV (cv2)
# and are fully compatible with Python 3.13
