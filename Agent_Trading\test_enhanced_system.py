#!/usr/bin/env python3
"""
Enhanced System Test - Comprehensive validation of all improvements
Tests the new timeframe selection, tool enforcement, and trade table features
"""

import sys
import os
import json
import traceback
from datetime import datetime

# Add the project root to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, current_dir)
sys.path.insert(0, parent_dir)

def test_timeframe_validation():
    """Test timeframe selection logic"""
    print("🧪 Testing Timeframe Selection Logic...")
    
    # Test positional timeframes
    positional_timeframes = ["15m", "1h", "1d"]
    scalp_timeframes = ["1m", "3m", "5m", "15m"]
    
    # Test smallest timeframe detection
    def get_smallest_timeframe(timeframes):
        timeframe_values = {
            "1m": 1, "3m": 3, "5m": 5, "15m": 15, "1h": 60, "1d": 1440
        }
        return min(timeframes, key=lambda x: timeframe_values.get(x, 999))
    
    assert get_smallest_timeframe(positional_timeframes) == "15m"
    assert get_smallest_timeframe(scalp_timeframes) == "1m"
    assert get_smallest_timeframe(["1h", "1d"]) == "1h"
    
    print("✅ Timeframe selection logic working correctly")
    return True

def test_prompt_enhancement():
    """Test enhanced prompt system"""
    print("🧪 Testing Enhanced Prompt System...")
    
    try:
        from helpers.prompts import combine_prompts, positional_prompt, scalp_prompt
        
        # Test prompt combination
        indian_combined = combine_prompts(positional_prompt, "Indian Market")
        crypto_combined = combine_prompts(scalp_prompt, "Crypto")
        
        # Check if enhancements are added
        assert "FII/DII" in indian_combined or "Indian" in indian_combined
        assert "crypto" in crypto_combined.lower() or "btc" in crypto_combined.lower()
        
        # Check tool enforcement
        assert "MANDATORY" in positional_prompt
        assert "STEP 1" in positional_prompt
        assert "STEP 2" in positional_prompt
        assert "STEP 3" in positional_prompt
        
        print("✅ Enhanced prompt system working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Prompt enhancement test failed: {e}")
        return False

def test_trade_table_structure():
    """Test trade table data structure"""
    print("🧪 Testing Trade Table Structure...")
    
    # Mock trade response structure
    mock_response = {
        "analysis_summary": "Bullish bias on Bank Nifty with strong support at 55,600",
        "trade_ideas": [
            {
                "Direction": "Long",
                "Entry_Price_Range": "55,600-55,650",
                "Stop_Loss": "55,850",
                "Take_Profit_1": "55,300",
                "Take_Profit_2": "55,000",
                "Risk_Reward_Ratio": "2.4",
                "Timeframe": "15M",
                "Entry_Condition": "Break above 55,650 with volume confirmation",
                "Confidence": "8"
            }
        ],
        "key_levels": {
            "support": ["55,600", "55,300"],
            "resistance": ["56,000", "56,400"]
        },
        "market_context": "FII buying and positive global cues support bullish sentiment",
        "risk_management": "Risk 1% of capital per trade, use trailing stop after TP1"
    }
    
    # Validate structure
    required_fields = ["analysis_summary", "trade_ideas", "key_levels", "market_context", "risk_management"]
    for field in required_fields:
        assert field in mock_response, f"Missing required field: {field}"
    
    # Validate trade idea structure
    trade = mock_response["trade_ideas"][0]
    trade_fields = ["Direction", "Entry_Price_Range", "Stop_Loss", "Take_Profit_1", "Take_Profit_2", 
                   "Risk_Reward_Ratio", "Timeframe", "Entry_Condition", "Confidence"]
    
    for field in trade_fields:
        assert field in trade, f"Missing trade field: {field}"
    
    print("✅ Trade table structure validation passed")
    return True

def test_tool_validation_logic():
    """Test tool execution validation"""
    print("🧪 Testing Tool Validation Logic...")
    
    # Mock tool usage logs
    complete_tools = [
        {"tool_name": "detect_trading_symbol", "result": "BANKNIFTY"},
        {"tool_name": "get_market_context_summary", "result": "Market data"},
        {"tool_name": "get_comprehensive_market_news", "result": "News data"},
        {"tool_name": "get_fii_dii_flows", "result": "FII/DII data"}
    ]
    
    incomplete_tools = [
        {"tool_name": "get_market_context_summary", "result": "Market data"}
    ]
    
    # Test validation logic
    required_tools = ["detect_trading_symbol", "get_market_context_summary", "get_comprehensive_market_news"]
    
    def validate_tools(tool_log):
        executed_tool_names = [tool['tool_name'] for tool in tool_log]
        missing_tools = [tool for tool in required_tools if tool not in executed_tool_names]
        return len(missing_tools) == 0, missing_tools
    
    # Test complete execution
    is_complete, missing = validate_tools(complete_tools)
    assert is_complete == True
    assert len(missing) == 0
    
    # Test incomplete execution
    is_complete, missing = validate_tools(incomplete_tools)
    assert is_complete == False
    assert len(missing) == 2
    
    print("✅ Tool validation logic working correctly")
    return True

def test_memory_system_integration():
    """Test memory system integration"""
    print("🧪 Testing Memory System Integration...")

    try:
        # Check if memory files exist
        memory_dir = os.path.join(os.path.dirname(__file__), "memory")
        required_files = ["analyses.json", "successful_patterns.json", "user_preferences.json"]

        for file_name in required_files:
            file_path = os.path.join(memory_dir, file_name)
            assert os.path.exists(file_path), f"Missing memory file: {file_name}"

        # Check if memory files are valid JSON
        for file_name in required_files:
            file_path = os.path.join(memory_dir, file_name)
            try:
                with open(file_path, 'r') as f:
                    json.load(f)
            except json.JSONDecodeError:
                print(f"⚠️ Warning: {file_name} is not valid JSON, but file exists")

        print("✅ Memory system integration working")
        return True

    except Exception as e:
        print(f"❌ Memory system test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_configuration():
    """Test API configuration and fallback"""
    print("🧪 Testing API Configuration...")
    
    try:
        import json

        # Load config.json directly
        config_path = os.path.join(os.path.dirname(__file__), "config.json")
        with open(config_path, 'r') as f:
            config = json.load(f)

        # Check required API configurations (case-insensitive)
        required_apis = {
            "google_api_key": "google_api_key",
            "dhan_client_id": "DHAN_CLIENT_ID",
            "dhan_access_token": "DHAN_ACCESS_TOKEN"
        }

        for config_key in required_apis.values():
            assert config_key in config, f"Missing API configuration: {config_key}"

        # Check model hierarchy (optional - may not be in config)
        if "gemini_model_hierarchy" in config:
            hierarchy = config["gemini_model_hierarchy"]
            assert len(hierarchy) >= 2, "Model hierarchy should have fallback options"
        else:
            # If not in config, that's okay - it might be hardcoded
            pass
        
        print("✅ API configuration validation passed")
        return True
        
    except Exception as e:
        print(f"❌ API configuration test failed: {e}")
        return False

def run_all_tests():
    """Run all enhanced system tests"""
    print("🚀 Starting Enhanced System Tests...")
    print("=" * 60)
    
    tests = [
        ("Timeframe Validation", test_timeframe_validation),
        ("Prompt Enhancement", test_prompt_enhancement),
        ("Trade Table Structure", test_trade_table_structure),
        ("Tool Validation Logic", test_tool_validation_logic),
        ("Memory System Integration", test_memory_system_integration),
        ("API Configuration", test_api_configuration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n📋 Running: {test_name}")
            result = test_func()
            results.append((test_name, result, None))
            
        except Exception as e:
            print(f"❌ {test_name} failed with error: {e}")
            print(f"Traceback: {traceback.format_exc()}")
            results.append((test_name, False, str(e)))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, result, error in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} - {test_name}")
        if error:
            print(f"   Error: {error}")
        
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n📈 Total: {len(results)} tests | ✅ Passed: {passed} | ❌ Failed: {failed}")
    
    if failed == 0:
        print("🎉 ALL TESTS PASSED! Enhanced system is ready for deployment.")
    else:
        print(f"⚠️ {failed} tests failed. Please review and fix issues before deployment.")
    
    return failed == 0

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
