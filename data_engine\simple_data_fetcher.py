"""
Simple Data Fetcher
===================

A simplified, reliable data fetching utility that bypasses complex logic
and focuses on getting data into the database efficiently.
"""

import pandas as pd
from datetime import datetime, timedelta
from typing import List, Optional
import time

from .market_data_feed import MarketDataFeed
from .database_manager import DatabaseManager
import sys
from pathlib import Path

# Add root directory to path for core imports
root_dir = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(root_dir))

from core.config_manager import ConfigManager
from core.logger import get_logger


class SimpleDataFetcher:
    """Simple, reliable data fetcher"""

    def __init__(self, config_manager: ConfigManager):
        """Initialize simple data fetcher

        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager
        self.logger = get_logger()

        # Initialize components
        self.market_feed = MarketDataFeed(config_manager)
        self.database = DatabaseManager(config_manager)

        self.logger.info("[OK] Simple data fetcher initialized")

    def fetch_and_store_data(self,
                           timeframes: List[str] = None,
                           days: int = 7,
                           symbol: str = "BTCUSD",
                           force_refresh: bool = False) -> bool:
        """Fetch and store data for specified timeframes

        Args:
            timeframes: List of timeframes to fetch (default: ['3m', '15m', '1h'])
            days: Number of days to fetch
            symbol: Trading symbol
            force_refresh: Whether to re-fetch existing data

        Returns:
            True if successful
        """
        if timeframes is None:
            timeframes = ['3m', '15m', '1h']

        self.logger.info(f"[START] Simple data fetch: {days} days, timeframes: {timeframes}")

        success_count = 0
        total_records = 0

        for timeframe in timeframes:
            try:
                self.logger.info(f"[FETCH] Processing {timeframe} timeframe...")

                # Check existing data if not forcing refresh
                if not force_refresh:
                    existing_count = self.database.get_record_count(symbol, timeframe)
                    if existing_count > 0:
                        self.logger.info(f"[SKIP] {timeframe} has {existing_count} records, skipping (use force_refresh=True to override)")
                        success_count += 1
                        continue

                # Fetch data
                df = self.market_feed.fetch_historical_data(
                    timeframe=timeframe,
                    days=days
                )

                if df.empty:
                    self.logger.warning(f"[WARNING] No data fetched for {timeframe}")
                    continue

                # Store data
                if self.database.store_market_data(df, symbol):
                    success_count += 1
                    total_records += len(df)
                    self.logger.info(f"[OK] Stored {len(df)} {timeframe} records")
                else:
                    self.logger.error(f"[ERROR] Failed to store {timeframe} data")

                # Small delay to avoid rate limiting
                time.sleep(0.5)

            except Exception as e:
                self.logger.error(f"[ERROR] Failed to process {timeframe}: {e}")
                continue

        # Summary
        success = success_count == len(timeframes)
        if success:
            self.logger.info(f"[SUCCESS] Fetched and stored {total_records} total records across {len(timeframes)} timeframes")
        else:
            self.logger.warning(f"[PARTIAL] {success_count}/{len(timeframes)} timeframes successful")

        return success

    def fetch_custom_range(self,
                          start_date: datetime,
                          end_date: datetime,
                          timeframes: List[str] = None,
                          symbol: str = "BTCUSD",
                          force_refresh: bool = False) -> bool:
        """Fetch data for a custom date range

        Args:
            start_date: Start date
            end_date: End date
            timeframes: List of timeframes
            symbol: Trading symbol
            force_refresh: Whether to re-fetch existing data

        Returns:
            True if successful
        """
        if timeframes is None:
            timeframes = ['3m', '15m', '1h']

        days = (end_date - start_date).days + 1
        self.logger.info(f"[START] Custom range fetch: {start_date.date()} to {end_date.date()} ({days} days)")

        success_count = 0
        total_records = 0

        for timeframe in timeframes:
            try:
                self.logger.info(f"[FETCH] Processing {timeframe} timeframe...")

                # Check existing data if not forcing refresh
                if not force_refresh:
                    existing_data = self.database.get_market_data(
                        symbol=symbol,
                        timeframe=timeframe,
                        start_date=start_date,
                        end_date=end_date,
                        limit=1
                    )
                    if not existing_data.empty:
                        self.logger.info(f"[SKIP] {timeframe} data exists for this range, skipping")
                        success_count += 1
                        continue

                # Fetch data with custom end time
                df = self.market_feed.fetch_historical_data(
                    timeframe=timeframe,
                    days=days,
                    end_time=end_date
                )

                if df.empty:
                    self.logger.warning(f"[WARNING] No data fetched for {timeframe}")
                    continue

                # Filter to requested range
                df = df[(df.index >= start_date) & (df.index <= end_date)]

                if df.empty:
                    self.logger.warning(f"[WARNING] No data in requested range for {timeframe}")
                    continue

                # Store data
                if self.database.store_market_data(df, symbol):
                    success_count += 1
                    total_records += len(df)
                    self.logger.info(f"[OK] Stored {len(df)} {timeframe} records")
                else:
                    self.logger.error(f"[ERROR] Failed to store {timeframe} data")

                time.sleep(0.5)

            except Exception as e:
                self.logger.error(f"[ERROR] Failed to process {timeframe}: {e}")
                continue

        # Summary
        success = success_count == len(timeframes)
        if success:
            self.logger.info(f"[SUCCESS] Custom range fetch completed: {total_records} total records")
        else:
            self.logger.warning(f"[PARTIAL] {success_count}/{len(timeframes)} timeframes successful")

        return success

    def get_database_summary(self) -> dict:
        """Get a summary of database contents

        Returns:
            Dictionary with database statistics
        """
        return self.database.get_database_info()

    def clear_all_data(self) -> bool:
        """Clear all data from database

        Returns:
            True if successful
        """
        return self.database.clear_all_data()
