"""Memory system for storing and learning from trading analyses and feedback."""

import json
import os
import hashlib
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from PIL import Image
import base64
import io


@dataclass
class AnalysisMemory:
    """Structure for storing analysis memories."""
    id: str
    timestamp: datetime
    image_hash: str
    symbol: Optional[str]
    analysis_type: str  # "visual", "technical", "crypto", etc.
    analysis_result: Dict[str, Any]
    user_feedback: Optional[Dict[str, Any]] = None
    trade_outcome: Optional[Dict[str, Any]] = None
    success_score: Optional[float] = None
    tags: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []


class TradingMemorySystem:
    """Memory system for storing and retrieving trading analysis experiences."""
    
    def __init__(self, memory_dir: str = "memory"):
        self.memory_dir = memory_dir
        self.analyses_file = os.path.join(memory_dir, "analyses.json")
        self.patterns_file = os.path.join(memory_dir, "successful_patterns.json")
        self.preferences_file = os.path.join(memory_dir, "user_preferences.json")
        self.images_dir = os.path.join(memory_dir, "images")
        
        # Create directories if they don't exist
        os.makedirs(memory_dir, exist_ok=True)
        os.makedirs(self.images_dir, exist_ok=True)
        
        # Initialize files if they don't exist
        self._initialize_files()
    
    def _initialize_files(self):
        """Initialize memory files if they don't exist."""
        files_to_init = [
            (self.analyses_file, []),
            (self.patterns_file, {}),
            (self.preferences_file, {
                "preferred_symbols": ["NSEI", "NSEBANK", "BTCUSD", "ETHUSD", "SOLUSD"],
                "risk_tolerance": "medium",
                "preferred_timeframes": ["15m", "1h", "4h"],
                "trading_style": "swing",
                "success_threshold": 0.7
            })
        ]
        
        for file_path, default_content in files_to_init:
            if not os.path.exists(file_path):
                with open(file_path, 'w') as f:
                    json.dump(default_content, f, indent=2, default=str)
    
    def _generate_image_hash(self, image: Image.Image) -> str:
        """Generate a hash for an image to identify similar images."""
        # Convert image to bytes
        img_byte_arr = io.BytesIO()
        image.save(img_byte_arr, format='PNG')
        img_byte_arr = img_byte_arr.getvalue()
        
        # Generate hash
        return hashlib.md5(img_byte_arr).hexdigest()
    
    def _save_image(self, image: Image.Image, image_hash: str) -> str:
        """Save image to memory directory."""
        image_path = os.path.join(self.images_dir, f"{image_hash}.png")
        if not os.path.exists(image_path):
            image.save(image_path)
        return image_path
    
    def store_analysis(self, 
                      image: Image.Image,
                      analysis_result: Dict[str, Any],
                      symbol: Optional[str] = None,
                      analysis_type: str = "visual") -> str:
        """
        Store a new analysis in memory.
        
        Args:
            image: The chart image analyzed
            analysis_result: The AI analysis result
            symbol: Trading symbol (if known)
            analysis_type: Type of analysis performed
            
        Returns:
            Analysis ID for future reference
        """
        # Generate image hash and save image
        image_hash = self._generate_image_hash(image)
        self._save_image(image, image_hash)
        
        # Create analysis memory
        analysis_id = f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{image_hash[:8]}"
        
        # Extract tool usage information for tracking
        tool_usage = analysis_result.get("tool_usage", [])
        optimization_stats = analysis_result.get("optimization_stats", {})

        memory = AnalysisMemory(
            id=analysis_id,
            timestamp=datetime.now(),
            image_hash=image_hash,
            symbol=symbol,
            analysis_type=analysis_type,
            analysis_result=analysis_result
        )

        # Add tool tracking metadata
        memory.analysis_result["memory_metadata"] = {
            "tools_used_count": len(tool_usage),
            "tools_used_names": [tool.get("tool_name") for tool in tool_usage],
            "parallel_execution": optimization_stats.get("parallel_execution", False),
            "cached_results": optimization_stats.get("cached_results", 0),
            "total_tools_executed": optimization_stats.get("total_tools_executed", 0)
        }
        
        # Load existing analyses
        with open(self.analyses_file, 'r') as f:
            analyses = json.load(f)
        
        # Add new analysis
        analyses.append(asdict(memory))
        
        # Save back to file
        with open(self.analyses_file, 'w') as f:
            json.dump(analyses, f, indent=2, default=str)
        
        return analysis_id
    
    def add_feedback(self, 
                    analysis_id: str, 
                    feedback: Dict[str, Any]) -> bool:
        """
        Add user feedback to an existing analysis.
        
        Args:
            analysis_id: ID of the analysis to update
            feedback: User feedback data
            
        Returns:
            True if successful, False if analysis not found
        """
        # Load analyses
        with open(self.analyses_file, 'r') as f:
            analyses = json.load(f)
        
        # Find and update the analysis
        for analysis in analyses:
            if analysis['id'] == analysis_id:
                analysis['user_feedback'] = feedback
                analysis['success_score'] = feedback.get('success_score', 0.5)
                
                # Add to successful patterns if score is high
                success_score = feedback.get('success_score', 0)
                if success_score is not None and success_score >= 0.7:
                    self._add_successful_pattern(analysis)
                
                # Save back to file
                with open(self.analyses_file, 'w') as f:
                    json.dump(analyses, f, indent=2, default=str)
                
                return True
        
        return False
    
    def _add_successful_pattern(self, analysis: Dict[str, Any]):
        """Add a successful analysis to the patterns database."""
        # Load patterns
        with open(self.patterns_file, 'r') as f:
            patterns = json.load(f)
        
        # Extract key pattern features
        symbol = analysis.get('symbol', 'unknown')
        analysis_type = analysis.get('analysis_type', 'visual')
        
        pattern_key = f"{symbol}_{analysis_type}"
        
        if pattern_key not in patterns:
            patterns[pattern_key] = {
                "symbol": symbol,
                "analysis_type": analysis_type,
                "successful_analyses": [],
                "success_rate": 0.0,
                "total_count": 0
            }
        
        # Add this analysis
        patterns[pattern_key]["successful_analyses"].append({
            "analysis_id": analysis['id'],
            "timestamp": analysis['timestamp'],
            "success_score": analysis.get('success_score', 0.7),
            "key_insights": analysis['analysis_result'].get('key_insights', [])
        })
        
        # Update success rate
        pattern_data = patterns[pattern_key]
        pattern_data["total_count"] += 1
        successful_count = len(pattern_data["successful_analyses"])
        pattern_data["success_rate"] = successful_count / pattern_data["total_count"]
        
        # Save patterns
        with open(self.patterns_file, 'w') as f:
            json.dump(patterns, f, indent=2, default=str)
    
    def get_similar_analyses(self, 
                           symbol: Optional[str] = None,
                           analysis_type: Optional[str] = None,
                           min_success_score: float = 0.6,
                           limit: int = 5) -> List[Dict[str, Any]]:
        """
        Get similar successful analyses for reference.
        
        Args:
            symbol: Filter by symbol
            analysis_type: Filter by analysis type
            min_success_score: Minimum success score
            limit: Maximum number of results
            
        Returns:
            List of similar successful analyses
        """
        # Load analyses
        with open(self.analyses_file, 'r') as f:
            analyses = json.load(f)
        
        # Filter analyses
        filtered = []
        for analysis in analyses:
            # Check if has feedback and meets success threshold
            if not analysis.get('user_feedback'):
                continue
            
            success_score = analysis.get('success_score', 0)
            if success_score < min_success_score:
                continue
            
            # Apply filters
            if symbol and analysis.get('symbol') != symbol:
                continue
            
            if analysis_type and analysis.get('analysis_type') != analysis_type:
                continue
            
            filtered.append(analysis)
        
        # Sort by success score and timestamp
        filtered.sort(key=lambda x: (x.get('success_score', 0), x.get('timestamp', '')), reverse=True)
        
        return filtered[:limit]
    
    def get_user_preferences(self) -> Dict[str, Any]:
        """Get user trading preferences."""
        with open(self.preferences_file, 'r') as f:
            return json.load(f)
    
    def update_user_preferences(self, preferences: Dict[str, Any]):
        """Update user trading preferences."""
        current_prefs = self.get_user_preferences()
        current_prefs.update(preferences)
        
        with open(self.preferences_file, 'w') as f:
            json.dump(current_prefs, f, indent=2)
    
    def get_analysis_stats(self) -> Dict[str, Any]:
        """Get statistics about stored analyses."""
        with open(self.analyses_file, 'r') as f:
            analyses = json.load(f)
        
        total_analyses = len(analyses)
        with_feedback = len([a for a in analyses if a.get('user_feedback')])
        successful = len([a for a in analyses if a.get('success_score') is not None and a.get('success_score', 0) >= 0.7])
        
        # Symbol breakdown
        symbol_counts = {}
        for analysis in analyses:
            symbol = analysis.get('symbol', 'unknown')
            symbol_counts[symbol] = symbol_counts.get(symbol, 0) + 1
        
        return {
            "total_analyses": total_analyses,
            "analyses_with_feedback": with_feedback,
            "successful_analyses": successful,
            "success_rate": successful / with_feedback if with_feedback > 0 else 0,
            "symbol_breakdown": symbol_counts
        }


# Global memory system instance
memory_system = TradingMemorySystem()


def get_memory_system() -> TradingMemorySystem:
    """Get the global memory system instance."""
    return memory_system
