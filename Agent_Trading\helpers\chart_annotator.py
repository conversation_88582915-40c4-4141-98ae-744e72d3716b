"""
Chart Annotation System
Adds entry, stop-loss, and take-profit levels directly on trading charts
"""

import io
import re
from PIL import Image, ImageDraw, ImageFont
from typing import Dict, Any, Optional, Tuple, List
import logging
import numpy as np

logger = logging.getLogger(__name__)

class ChartAnnotator:
    """Annotates trading charts with entry/exit levels and trading signals"""
    
    def __init__(self):
        self.colors = {
            'entry_buy': '#00FF00',      # Green for buy entry
            'entry_sell': '#FF0000',     # Red for sell entry
            'stop_loss': '#FF6B6B',      # Light red for stop loss
            'take_profit': '#4ECDC4',    # Teal for take profit
            'support': '#FFD93D',        # Yellow for support
            'resistance': '#6BCF7F',     # Light green for resistance
            'text': '#FFFFFF',           # White text
            'background': '#000000'      # Black background for text
        }
        
        # Try to load a font, fallback to default if not available
        try:
            self.font = ImageFont.truetype("arial.ttf", 12)
            self.font_large = ImageFont.truetype("arial.ttf", 16)
        except:
            self.font = ImageFont.load_default()
            self.font_large = ImageFont.load_default()
    
    def annotate_chart(self, image_data: bytes, analysis_result: Dict[str, Any]) -> bytes:
        """
        Annotate chart with trading levels and signals
        
        Args:
            image_data: Original chart image bytes
            analysis_result: Analysis containing entry/exit levels
            
        Returns:
            Annotated image bytes
        """
        try:
            # Load image
            image = Image.open(io.BytesIO(image_data))
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Extract trading levels from analysis
            levels = self._extract_trading_levels(analysis_result)
            
            if not levels:
                logger.warning("No trading levels found in analysis result")
                return image_data
            
            # Create drawing context
            draw = ImageDraw.Draw(image)
            width, height = image.size
            
            # Estimate price scale area (usually right side of chart)
            price_area_start = int(width * 0.85)  # Assume price scale starts at 85% of width
            chart_area_end = int(width * 0.83)    # Chart area ends at 83%
            
            # Draw trading levels
            self._draw_trading_levels(draw, levels, width, height, chart_area_end)
            
            # Add trading signal box
            self._add_signal_box(draw, analysis_result, width, height)
            
            # Add timestamp and confidence
            self._add_metadata(draw, analysis_result, width, height)
            
            # Convert back to bytes - ensure RGB mode for JPEG
            if image.mode in ('RGBA', 'LA', 'P'):
                # Convert RGBA/LA/P to RGB for JPEG compatibility
                rgb_image = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'P':
                    image = image.convert('RGBA')
                rgb_image.paste(image, mask=image.split()[-1] if image.mode in ('RGBA', 'LA') else None)
                image = rgb_image

            output_buffer = io.BytesIO()
            image.save(output_buffer, format='JPEG', quality=95)
            return output_buffer.getvalue()
            
        except Exception as e:
            logger.error(f"Chart annotation failed: {e}")
            return image_data  # Return original image if annotation fails
    
    def _extract_trading_levels(self, analysis_result: Dict[str, Any]) -> Dict[str, float]:
        """Extract price levels from analysis result"""
        levels = {}
        
        # Get the main analysis text
        analysis_text = ""
        if isinstance(analysis_result, dict):
            analysis_text = analysis_result.get('answer', '') or analysis_result.get('analysis', '') or str(analysis_result)
        else:
            analysis_text = str(analysis_result)
        
        # Extract price levels using regex patterns
        price_patterns = {
            'entry': [
                r'entry[:\s]+(\d+\.?\d*)',
                r'buy[:\s]+(\d+\.?\d*)',
                r'sell[:\s]+(\d+\.?\d*)',
                r'enter[:\s]+(\d+\.?\d*)'
            ],
            'stop_loss': [
                r'stop[:\s]*loss[:\s]+(\d+\.?\d*)',
                r'sl[:\s]+(\d+\.?\d*)',
                r'stop[:\s]+(\d+\.?\d*)'
            ],
            'take_profit': [
                r'take[:\s]*profit[:\s]+(\d+\.?\d*)',
                r'tp[:\s]+(\d+\.?\d*)',
                r'target[:\s]+(\d+\.?\d*)',
                r'profit[:\s]+(\d+\.?\d*)'
            ],
            'support': [
                r'support[:\s]+(\d+\.?\d*)',
                r'support[:\s]*level[:\s]+(\d+\.?\d*)'
            ],
            'resistance': [
                r'resistance[:\s]+(\d+\.?\d*)',
                r'resistance[:\s]*level[:\s]+(\d+\.?\d*)'
            ]
        }
        
        text_lower = analysis_text.lower()
        
        for level_type, patterns in price_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, text_lower)
                if matches:
                    try:
                        levels[level_type] = float(matches[0])
                        break  # Use first match found
                    except ValueError:
                        continue
        
        return levels
    
    def _draw_trading_levels(self, draw: ImageDraw.Draw, levels: Dict[str, float], 
                           width: int, height: int, chart_end: int):
        """Draw horizontal lines for trading levels"""
        
        # Estimate price range from chart (this is approximate)
        # In a real implementation, you'd need to detect the actual price scale
        if not levels:
            return
        
        # Get price range
        prices = list(levels.values())
        min_price = min(prices)
        max_price = max(prices)
        price_range = max_price - min_price if max_price != min_price else max_price * 0.1
        
        # Expand range by 10% for better visualization
        expanded_range = price_range * 1.2
        chart_min = min_price - expanded_range * 0.1
        chart_max = max_price + expanded_range * 0.1
        
        # Chart area (assume 10% margin from top/bottom)
        chart_top = int(height * 0.1)
        chart_bottom = int(height * 0.9)
        chart_height = chart_bottom - chart_top
        
        # Draw each level
        for level_type, price in levels.items():
            if price <= 0:
                continue
                
            # Calculate Y position (inverted because image coordinates)
            y_ratio = (chart_max - price) / (chart_max - chart_min)
            y_pos = int(chart_top + y_ratio * chart_height)
            
            # Ensure Y position is within chart bounds
            y_pos = max(chart_top, min(chart_bottom, y_pos))
            
            # Get color for this level type
            color = self.colors.get(level_type, '#FFFFFF')
            
            # Draw horizontal line
            line_width = 3 if level_type == 'entry' else 2
            draw.line([(50, y_pos), (chart_end, y_pos)], fill=color, width=line_width)
            
            # Add price label
            label = f"{level_type.replace('_', ' ').title()}: {price:.2f}"
            
            # Draw text background
            text_bbox = draw.textbbox((0, 0), label, font=self.font)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]
            
            label_x = chart_end + 5
            label_y = y_pos - text_height // 2
            
            # Background rectangle
            draw.rectangle([
                (label_x - 2, label_y - 2),
                (label_x + text_width + 2, label_y + text_height + 2)
            ], fill=self.colors['background'])
            
            # Text
            draw.text((label_x, label_y), label, fill=color, font=self.font)
    
    def _add_signal_box(self, draw: ImageDraw.Draw, analysis_result: Dict[str, Any], 
                       width: int, height: int):
        """Add trading signal summary box"""
        
        # Extract signal information
        signal_info = self._extract_signal_info(analysis_result)
        
        if not signal_info:
            return
        
        # Position for signal box (top-left corner)
        box_x = 20
        box_y = 20
        box_width = 250
        box_height = 120
        
        # Draw background
        draw.rectangle([
            (box_x, box_y),
            (box_x + box_width, box_y + box_height)
        ], fill=self.colors['background'], outline='#FFFFFF', width=2)
        
        # Add signal text
        y_offset = box_y + 10
        line_height = 16
        
        for line in signal_info:
            draw.text((box_x + 10, y_offset), line, fill=self.colors['text'], font=self.font)
            y_offset += line_height
    
    def _extract_signal_info(self, analysis_result: Dict[str, Any]) -> List[str]:
        """Extract key signal information for display"""
        info_lines = []
        
        # Get analysis text
        analysis_text = ""
        if isinstance(analysis_result, dict):
            analysis_text = analysis_result.get('answer', '') or analysis_result.get('analysis', '') or str(analysis_result)
        else:
            analysis_text = str(analysis_result)
        
        text_lower = analysis_text.lower()
        
        # Extract signal direction
        if 'buy' in text_lower or 'bullish' in text_lower or 'long' in text_lower:
            info_lines.append("🟢 SIGNAL: BUY")
        elif 'sell' in text_lower or 'bearish' in text_lower or 'short' in text_lower:
            info_lines.append("🔴 SIGNAL: SELL")
        else:
            info_lines.append("🟡 SIGNAL: WAIT")
        
        # Extract timeframe if mentioned
        timeframes = ['1m', '5m', '15m', '1h', '4h', '1d']
        for tf in timeframes:
            if tf in text_lower:
                info_lines.append(f"⏰ TIMEFRAME: {tf.upper()}")
                break
        
        # Extract confidence if mentioned
        confidence_match = re.search(r'confidence[:\s]+(\d+)', text_lower)
        if confidence_match:
            confidence = confidence_match.group(1)
            info_lines.append(f"📊 CONFIDENCE: {confidence}%")
        
        # Extract risk/reward if mentioned
        rr_match = re.search(r'risk[:\s]*reward[:\s]+(\d+\.?\d*)', text_lower)
        if rr_match:
            rr_ratio = rr_match.group(1)
            info_lines.append(f"⚖️ R:R RATIO: 1:{rr_ratio}")
        
        return info_lines[:6]  # Limit to 6 lines to fit in box
    
    def _add_metadata(self, draw: ImageDraw.Draw, analysis_result: Dict[str, Any], 
                     width: int, height: int):
        """Add timestamp and analysis metadata"""
        
        # Position at bottom-right
        metadata_lines = []
        
        # Add timestamp
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        metadata_lines.append(f"Analysis: {timestamp}")
        
        # Add model info if available
        if isinstance(analysis_result, dict):
            model = analysis_result.get('model', 'AI Analysis')
            metadata_lines.append(f"Model: {model}")
        
        # Calculate position
        line_height = 14
        total_height = len(metadata_lines) * line_height
        start_y = height - total_height - 20
        
        for i, line in enumerate(metadata_lines):
            text_bbox = draw.textbbox((0, 0), line, font=self.font)
            text_width = text_bbox[2] - text_bbox[0]
            
            x_pos = width - text_width - 20
            y_pos = start_y + i * line_height
            
            # Semi-transparent background
            draw.rectangle([
                (x_pos - 5, y_pos - 2),
                (x_pos + text_width + 5, y_pos + 12)
            ], fill='#000000AA')
            
            draw.text((x_pos, y_pos), line, fill='#CCCCCC', font=self.font)
    
    def create_level_summary_image(self, levels: Dict[str, float], 
                                 signal_info: Dict[str, Any]) -> bytes:
        """Create a summary image showing all trading levels"""
        
        # Create a new image for the summary
        img_width = 400
        img_height = 300
        image = Image.new('RGB', (img_width, img_height), color='#1E1E1E')
        draw = ImageDraw.Draw(image)
        
        # Title
        title = "Trading Levels Summary"
        draw.text((20, 20), title, fill='#FFFFFF', font=self.font_large)
        
        # Draw levels
        y_offset = 60
        line_height = 25
        
        for level_type, price in levels.items():
            color = self.colors.get(level_type, '#FFFFFF')
            label = f"{level_type.replace('_', ' ').title()}: {price:.2f}"
            
            # Draw colored indicator
            draw.rectangle([(20, y_offset), (35, y_offset + 15)], fill=color)
            
            # Draw text
            draw.text((45, y_offset), label, fill='#FFFFFF', font=self.font)
            y_offset += line_height
        
        # Convert to bytes
        output_buffer = io.BytesIO()
        image.save(output_buffer, format='PNG')
        return output_buffer.getvalue()

# Global instance
chart_annotator = ChartAnnotator()
