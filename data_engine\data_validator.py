"""
Professional Data Validator
===========================

Institutional-grade data quality validation:
- OHLCV data integrity checks
- Missing data detection and handling
- Outlier detection and filtering
- Data consistency validation
- Quality scoring and reporting
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging
from dataclasses import dataclass

import sys
from pathlib import Path

# Add root directory to path for core imports
root_dir = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(root_dir))

from core.logger import get_logger

@dataclass
class DataQualityReport:
    """Data quality assessment report"""
    total_records: int
    valid_records: int
    invalid_records: int
    missing_data_percentage: float
    outlier_percentage: float
    quality_score: float
    issues: List[str]
    recommendations: List[str]

class DataValidator:
    """Professional data quality validator"""

    def __init__(self):
        """Initialize data validator"""
        self.logger = get_logger()

        # Quality thresholds
        self.quality_thresholds = {
            'min_quality_score': 0.85,  # 85% minimum quality
            'max_missing_percentage': 0.05,  # 5% max missing data
            'max_outlier_percentage': 0.02,  # 2% max outliers
            'max_price_change': 0.20,  # 20% max price change per candle
            'min_volume_threshold': 0.01  # Minimum volume threshold
        }

        self.logger.info("[OK] Data validator initialized")

    def validate_ohlcv_data(self, df: pd.DataFrame) -> DataQualityReport:
        """Comprehensive OHLCV data validation

        Args:
            df: DataFrame with OHLCV data

        Returns:
            Data quality report
        """
        if df.empty:
            return DataQualityReport(
                total_records=0,
                valid_records=0,
                invalid_records=0,
                missing_data_percentage=100.0,
                outlier_percentage=0.0,
                quality_score=0.0,
                issues=["No data provided"],
                recommendations=["Fetch data from source"]
            )

        total_records = len(df)
        issues = []
        recommendations = []

        # 1. Check for required columns
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            issues.append(f"Missing required columns: {missing_columns}")
            recommendations.append("Ensure all OHLCV columns are present")

        # 2. Check for missing values
        missing_data = df[required_columns].isnull().sum()
        missing_percentage = (missing_data.sum() / (len(df) * len(required_columns))) * 100

        if missing_percentage > 0:
            issues.append(f"Missing data: {missing_percentage:.2f}%")
            if missing_percentage > self.quality_thresholds['max_missing_percentage'] * 100:
                recommendations.append("Fill or interpolate missing values")

        # 3. Validate OHLC relationships
        invalid_ohlc = self._validate_ohlc_relationships(df)
        if invalid_ohlc > 0:
            issues.append(f"Invalid OHLC relationships: {invalid_ohlc} records")
            recommendations.append("Fix OHLC relationship violations")

        # 4. Detect price outliers
        outliers = self._detect_price_outliers(df)
        outlier_percentage = (len(outliers) / total_records) * 100

        if outlier_percentage > 0:
            issues.append(f"Price outliers detected: {outlier_percentage:.2f}%")
            if outlier_percentage > self.quality_thresholds['max_outlier_percentage'] * 100:
                recommendations.append("Review and filter extreme price movements")

        # 5. Check volume data
        volume_issues = self._validate_volume_data(df)
        if volume_issues:
            issues.extend(volume_issues)
            recommendations.append("Validate volume data source")

        # 6. Check for data gaps
        gaps = self._detect_data_gaps(df)
        if gaps:
            issues.append(f"Data gaps detected: {len(gaps)} gaps")
            recommendations.append("Fill data gaps with appropriate method")

        # 7. Calculate quality score
        valid_records = total_records - invalid_ohlc - len(outliers)
        quality_score = self._calculate_quality_score(
            total_records, valid_records, missing_percentage, outlier_percentage
        )

        # 8. Generate recommendations based on quality score
        if quality_score < self.quality_thresholds['min_quality_score']:
            recommendations.append("Data quality below acceptable threshold - review data source")

        report = DataQualityReport(
            total_records=total_records,
            valid_records=valid_records,
            invalid_records=total_records - valid_records,
            missing_data_percentage=missing_percentage,
            outlier_percentage=outlier_percentage,
            quality_score=quality_score,
            issues=issues,
            recommendations=recommendations
        )

        self.logger.info(f"[OK] Data validation completed - Quality Score: {quality_score:.2f}")
        return report

    def _validate_ohlc_relationships(self, df: pd.DataFrame) -> int:
        """Validate OHLC price relationships

        Args:
            df: DataFrame with OHLCV data

        Returns:
            Number of invalid records
        """
        invalid_count = 0

        # High should be >= Open, Low, Close
        invalid_count += len(df[df['high'] < df['open']])
        invalid_count += len(df[df['high'] < df['low']])
        invalid_count += len(df[df['high'] < df['close']])

        # Low should be <= Open, High, Close
        invalid_count += len(df[df['low'] > df['open']])
        invalid_count += len(df[df['low'] > df['high']])
        invalid_count += len(df[df['low'] > df['close']])

        # Prices should be positive
        invalid_count += len(df[(df['open'] <= 0) | (df['high'] <= 0) |
                               (df['low'] <= 0) | (df['close'] <= 0)])

        return invalid_count

    def _detect_price_outliers(self, df: pd.DataFrame) -> pd.Index:
        """Detect price outliers using statistical methods

        Args:
            df: DataFrame with OHLCV data

        Returns:
            Index of outlier records
        """
        outliers = pd.Index([])

        if len(df) < 10:  # Need minimum data for outlier detection
            return outliers

        # Calculate price changes
        df_temp = df.copy()
        df_temp['price_change'] = df_temp['close'].pct_change().abs()

        # Method 1: Extreme price changes
        extreme_changes = df_temp[
            df_temp['price_change'] > self.quality_thresholds['max_price_change']
        ].index
        outliers = outliers.union(extreme_changes)

        # Method 2: Statistical outliers (IQR method)
        for col in ['open', 'high', 'low', 'close']:
            Q1 = df_temp[col].quantile(0.25)
            Q3 = df_temp[col].quantile(0.75)
            IQR = Q3 - Q1

            lower_bound = Q1 - 3 * IQR  # 3 IQR for extreme outliers
            upper_bound = Q3 + 3 * IQR

            col_outliers = df_temp[
                (df_temp[col] < lower_bound) | (df_temp[col] > upper_bound)
            ].index
            outliers = outliers.union(col_outliers)

        return outliers

    def _validate_volume_data(self, df: pd.DataFrame) -> List[str]:
        """Validate volume data quality

        Args:
            df: DataFrame with OHLCV data

        Returns:
            List of volume-related issues
        """
        issues = []

        # Check for negative volume
        negative_volume = len(df[df['volume'] < 0])
        if negative_volume > 0:
            issues.append(f"Negative volume values: {negative_volume} records")

        # Check for zero volume
        zero_volume = len(df[df['volume'] == 0])
        if zero_volume > len(df) * 0.1:  # More than 10% zero volume
            issues.append(f"High percentage of zero volume: {zero_volume} records")

        # Check for extremely low volume
        if len(df) > 0:
            median_volume = df['volume'].median()
            low_volume = len(df[df['volume'] < median_volume * self.quality_thresholds['min_volume_threshold']])
            if low_volume > len(df) * 0.05:  # More than 5% extremely low volume
                issues.append(f"Extremely low volume detected: {low_volume} records")

        return issues

    def _detect_data_gaps(self, df: pd.DataFrame) -> List[Tuple[datetime, datetime]]:
        """Detect gaps in time series data

        Args:
            df: DataFrame with timestamp index

        Returns:
            List of gap periods (start, end)
        """
        gaps = []

        if len(df) < 2:
            return gaps

        # Infer expected frequency from first few intervals
        time_diffs = df.index.to_series().diff().dropna()
        if len(time_diffs) == 0:
            return gaps

        expected_freq = time_diffs.mode().iloc[0] if len(time_diffs.mode()) > 0 else time_diffs.median()

        # Allow some tolerance (1.5x expected frequency)
        max_gap = expected_freq * 1.5

        # Find gaps larger than expected
        for i in range(1, len(df)):
            time_diff = df.index[i] - df.index[i-1]
            if time_diff > max_gap:
                gaps.append((df.index[i-1], df.index[i]))

        return gaps

    def _calculate_quality_score(self, total_records: int, valid_records: int,
                                missing_percentage: float, outlier_percentage: float) -> float:
        """Calculate overall data quality score

        Args:
            total_records: Total number of records
            valid_records: Number of valid records
            missing_percentage: Percentage of missing data
            outlier_percentage: Percentage of outliers

        Returns:
            Quality score (0-1)
        """
        if total_records == 0:
            return 0.0

        # Base score from valid records
        validity_score = valid_records / total_records

        # Penalty for missing data
        missing_penalty = min(missing_percentage / 100, 0.5)  # Max 50% penalty

        # Penalty for outliers
        outlier_penalty = min(outlier_percentage / 100, 0.3)  # Max 30% penalty

        # Calculate final score
        quality_score = validity_score * (1 - missing_penalty) * (1 - outlier_penalty)

        return max(0.0, min(1.0, quality_score))

    def clean_data(self, df: pd.DataFrame, report: DataQualityReport) -> pd.DataFrame:
        """Clean data based on validation report

        Args:
            df: Original DataFrame
            report: Data quality report

        Returns:
            Cleaned DataFrame
        """
        if df.empty:
            return df

        cleaned_df = df.copy()

        # Remove records with invalid OHLC relationships
        mask = (
            (cleaned_df['high'] >= cleaned_df['open']) &
            (cleaned_df['high'] >= cleaned_df['low']) &
            (cleaned_df['high'] >= cleaned_df['close']) &
            (cleaned_df['low'] <= cleaned_df['open']) &
            (cleaned_df['low'] <= cleaned_df['high']) &
            (cleaned_df['low'] <= cleaned_df['close']) &
            (cleaned_df['open'] > 0) &
            (cleaned_df['high'] > 0) &
            (cleaned_df['low'] > 0) &
            (cleaned_df['close'] > 0) &
            (cleaned_df['volume'] >= 0)
        )

        cleaned_df = cleaned_df[mask]

        # Remove extreme outliers
        outliers = self._detect_price_outliers(cleaned_df)
        cleaned_df = cleaned_df.drop(outliers, errors='ignore')

        # Fill missing values with forward fill then backward fill
        cleaned_df = cleaned_df.fillna(method='ffill').fillna(method='bfill')

        self.logger.info(f"[OK] Data cleaned: {len(df)} -> {len(cleaned_df)} records")

        return cleaned_df
