"""Utility functions for the dashboard application."""

from __future__ import annotations

import base64
import json
import os
import time
import random
import re
import logging
from .parallel_utils import execute_tools_parallel, create_summarized_context, tool_cache
from io import BytesIO
from typing import List, Tuple, Dict, Any
from datetime import datetime

import streamlit as st

try:
    import ollama
except ImportError:
    ollama = None

import google.generativeai as genai
from PIL import Image
try:
    import pandas as pd
except ImportError:
    pd = None
from .tool_manager import get_tool_registry
import yfinance as yf
import warnings
from contextlib import redirect_stderr
import io

# Set up logger
logger = logging.getLogger(__name__)


def load_google_api_key() -> str | None:
    """Load the Google API key from ``config.json`` if available."""
    google_api_key = None

    # Get the project root directory (where this utils.py file is located)
    current_file_dir = os.path.dirname(os.path.abspath(__file__))  # helpers/
    project_root = os.path.dirname(current_file_dir)  # Agent_Trading/

    # Look for config.json in multiple locations
    config_paths = [
        os.path.join(project_root, "config.json"),  # Agent_Trading/config.json
        os.path.join(os.getcwd(), "config.json"),   # Current working directory
        os.path.join(os.path.dirname(os.getcwd()), "config.json")  # Parent directory
    ]

    found_path = None
    for path in config_paths:
        if os.path.exists(path):
            found_path = path
            break

    if found_path:
        try:
            with open(found_path, "r") as f:
                config = json.load(f)
                google_api_key = config.get("google_api_key")
        except json.JSONDecodeError:
            st.error("Error: Invalid JSON in config.json. Please check its format.")
        except Exception as exc:  # pragma: no cover - Streamlit UI
            st.error(f"An unexpected error occurred while loading config.json: {exc}")
    else:
        st.error(
            "Error: config.json not found in any checked location. "
            f"Please create it and add your Gemini API key. Checked paths: {', '.join(config_paths)}"
        )

    return google_api_key


@st.cache_data(ttl=60)
def check_ollama_status() -> Tuple[bool, List[str]]:
    """Check if the Ollama server is running and list available models."""
    if not ollama:
        return False, []
    try:
        client = ollama.Client()
        models = client.list()["models"]
        return True, [model["name"] for model in models]
    except Exception:  # pragma: no cover - depends on external service
        return False, []


def encode_images_to_base64(images: List[Image.Image]) -> List[str]:
    """Encode a list of ``PIL.Image`` objects as base64 strings."""
    encoded = []
    for img in images:
        buffer = BytesIO()
        img.save(buffer, format="PNG")
        encoded.append(base64.b64encode(buffer.getvalue()).decode("utf-8"))
    return encoded


def compress_image_if_needed(image: Image.Image, max_size_mb: float = 4.0) -> Image.Image:
    """Compress image if it exceeds the maximum size limit."""
    # Convert to bytes to check size
    buffer = BytesIO()
    image.save(buffer, format="PNG")
    size_mb = len(buffer.getvalue()) / (1024 * 1024)

    if size_mb <= max_size_mb:
        return image

    # Calculate compression ratio needed
    compression_ratio = max_size_mb / size_mb
    new_width = int(image.width * (compression_ratio ** 0.5))
    new_height = int(image.height * (compression_ratio ** 0.5))

    # Resize image
    compressed = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

    # Verify new size
    buffer = BytesIO()
    compressed.save(buffer, format="PNG")
    new_size_mb = len(buffer.getvalue()) / (1024 * 1024)

    st.info(f"🗜️ Image compressed from {size_mb:.1f}MB to {new_size_mb:.1f}MB")
    return compressed


def validate_request_size(images: List[Image.Image], prompt: str) -> bool:
    """Validate that the total request size is within limits."""
    total_size = len(prompt.encode('utf-8'))

    for img in images:
        buffer = BytesIO()
        img.save(buffer, format="PNG")
        total_size += len(buffer.getvalue())

    # Gemini has a ~20MB limit, we'll use 15MB to be safe
    max_size = 15 * 1024 * 1024

    if total_size > max_size:
        st.error(f"❌ Request too large: {total_size / (1024*1024):.1f}MB (max: {max_size / (1024*1024):.1f}MB)")
        return False

    return True


def generate_analysis_cloud_optimized(images: List[Image.Image], prompt: str, api_key: str, model_name: str, progress_callback=None, thinking_mode: str = "dynamic") -> dict:
    """
    Consolidated analysis with thinking capabilities and tool execution.
    Replaces the dual-system architecture with a single unified pipeline.
    Includes automatic model fallback for quota limits and enhanced reasoning.
    """

    def update_progress(message: str, step: int = None, total_steps: int = None):
        if progress_callback:
            progress_callback(message, step, total_steps)

    update_progress("🚀 Starting consolidated analysis with thinking...", 1, 10)

    # Model fallback hierarchy for quota limits
    model_fallback_chain = [
        model_name,  # Original requested model
        "gemini-2.5-flash",  # First fallback
        "gemini-2.0-flash"   # Final fallback
    ]
    
    # Configure thinking budget based on mode
    thinking_config = {
        "dynamic": {"max_thinking_tokens": 30000, "enable_thinking": True},
        "conservative": {"max_thinking_tokens": 15000, "enable_thinking": True},
        "aggressive": {"max_thinking_tokens": 50000, "enable_thinking": True},
        "disabled": {"max_thinking_tokens": 0, "enable_thinking": False}
    }
    
    current_thinking_config = thinking_config.get(thinking_mode, thinking_config["dynamic"])
    
    # Remove duplicates while preserving order
    unique_models = []
    for model in model_fallback_chain:
        if model not in unique_models:
            unique_models.append(model)
    model_fallback_chain = unique_models

    # Helper function for progress updates
    max_retries = 3
    base_delay = 1.0
    tool_usage_log = []

    try:
        # Configure API
        update_progress("🔑 Configuring Gemini API...", 2, 8)
        genai.configure(api_key=api_key)
        
        # Validate and compress images
        update_progress("🖼️ Processing images...", 3, 8)
        processed_images = []
        for img in images:
            compressed_img = compress_image_if_needed(img)
            processed_images.append(compressed_img)

        # Validate request size
        if not validate_request_size(processed_images, prompt):
            return {"error": "Request too large. Please use smaller images or shorter prompt."}

        # Get available tools first
        from .tool_manager import get_tool_registry
        registry = get_tool_registry()
        tool_names = registry.get_tool_names()
        tool_definitions = registry.get_tool_definitions()

        # Debug logging
        update_progress(f"🔧 Found {len(tool_names)} tools: {', '.join(tool_names[:5])}{'...' if len(tool_names) > 5 else ''}", 3, 8)

        # Create dynamic mapping for all registered tools
        tool_functions = {}
        for tool_name in tool_names:
            # Use closure to capture tool_name correctly and handle errors
            def create_tool_function(name):
                def tool_function(**kwargs):
                    try:
                        result = registry.execute_tool(name, **kwargs)
                        if result.success:
                            return result.data
                        else:
                            # Return error info instead of empty data
                            raise Exception(f"Tool {name} failed: {result.error}")
                    except Exception as e:
                        raise Exception(f"Tool execution error for {name}: {str(e)}")
                return tool_function
            tool_functions[tool_name] = create_tool_function(tool_name)
        
        update_progress(f"🔧 Created {len(tool_functions)} tool functions", 3, 8)

        # Initialize model WITH TOOLS and thinking capabilities
        update_progress("🤖 Initializing AI model with tools and thinking...", 4, 10)
        
        # Enhanced prompt with thinking instructions if enabled
        enhanced_prompt = prompt
        if current_thinking_config["enable_thinking"]:
            thinking_instructions = f"""
            
<thinking>
Before providing your analysis, think through the chart systematically:
1. Identify chart type, timeframe, and key technical patterns
2. Analyze price action, support/resistance levels, and trend structure  
3. Evaluate momentum indicators and volume patterns
4. Consider risk management and entry/exit strategies
5. Determine if additional market data tools would be helpful

Maximum thinking tokens: {current_thinking_config["max_thinking_tokens"]}
</thinking>

{prompt}

Remember to provide detailed price levels, entry/exit points, and risk management in your final analysis.
"""
            enhanced_prompt = thinking_instructions
        
        # Create model with tools - Gemini expects tools as a list directly
        try:
            model = genai.GenerativeModel(
                model_name=model_name,
                tools=tool_definitions  # Pass the FunctionDeclaration objects directly
            )
            update_progress(f"✅ Model initialized successfully with {len(tool_definitions)} tools", 4, 10)
        except Exception as e:
            update_progress(f"❌ Model initialization failed: {str(e)}", 4, 10)
            return {"error": f"Model initialization failed: {str(e)}"}

        # Start analysis with retry logic
        for attempt in range(max_retries):
            try:
                update_progress("💬 Starting chart analysis with thinking...", 5, 10)

                # Prepare content for analysis
                content_parts = [enhanced_prompt]  # Use enhanced prompt with thinking instructions
                for img in processed_images:
                    content_parts.append(img)

                # Send initial request
                chat = model.start_chat()
                update_progress("📤 Sending initial message to AI...", 5, 10)
                response = chat.send_message(content_parts)
                update_progress("📥 Received initial response from AI", 6, 10)

                # Process response and handle tool calls
                max_iterations = 10
                thinking_content = ""
                
                for iteration in range(max_iterations):
                    
                    update_progress(f"🔄 Processing AI response (iteration {iteration + 1}/{max_iterations})...", 7, 10)
                    
                    # Extract thinking content if present
                    if response.candidates and response.candidates[0].content.parts:
                        for part in response.candidates[0].content.parts:
                            if hasattr(part, 'text') and part.text and "<thinking>" in part.text:
                                # Extract thinking content
                                thinking_match = re.search(r'<thinking>(.*?)</thinking>', part.text, re.DOTALL)
                                if thinking_match:
                                    thinking_content = thinking_match.group(1).strip()
                                    update_progress("🧠 AI thinking process captured", 7, 10)
                    
                    # Check for function calls in response
                    if response.candidates and response.candidates[0].content.parts:
                        has_function_call = False
                        has_text_response = False
                        text_content = ""
                        
                        for part in response.candidates[0].content.parts:
                            if hasattr(part, 'function_call') and part.function_call:
                                has_function_call = True
                                func_call = part.function_call
                                func_name = func_call.name

                                update_progress(f"🔧 AI requested tool: {func_name}", 7, 10)

                                if func_name in tool_functions:
                                    update_progress(f"🔧 Executing tool: {func_name}...", 7, 10)
                                    
                                    try:
                                        # Execute function
                                        func_args = dict(func_call.args)
                                        update_progress(f"🔧 Executing {func_name} with args: {list(func_args.keys())}", 7, 10)
                                        result = tool_functions[func_name](**func_args)

                                        # Use Gemini 2.5 Flash for intelligent summarization
                                        from .parallel_utils import summarize_tool_result
                                        result_summary = summarize_tool_result(func_name, result, use_llm_summary=True)

                                        # Log tool usage with intelligent summary
                                        tool_usage_log.append({
                                            "tool_name": func_name,
                                            "arguments": func_args,
                                            "result_summary": result_summary,
                                            "timestamp": datetime.now().isoformat(),
                                            "cached": False,
                                            "execution_time": 0
                                        })
                                        
                                        update_progress(f"✅ {func_name} executed successfully", 7, 10)

                                        # Send result back to model
                                        function_response = genai.protos.FunctionResponse(
                                            name=func_name,
                                            response={"result": result}
                                        )
                                        
                                        update_progress(f"📤 Sending {func_name} result back to AI...", 7, 10)
                                        response = chat.send_message(
                                            genai.protos.Part(function_response=function_response)
                                        )
                                        update_progress(f"📥 AI processed {func_name} result", 7, 10)
                                        break  # Process new response

                                    except Exception as e:
                                        error_msg = f"Function {func_name} failed: {str(e)}"
                                        update_progress(f"❌ Tool error: {error_msg}", 7, 10)
                                        return {"error": error_msg, "tool_usage": tool_usage_log}

                                else:
                                    error_msg = f"Unknown function requested: {func_name}"
                                    update_progress(f"❌ Unknown tool: {error_msg}", 7, 10)
                                    return {"error": error_msg, "available_tools": list(tool_functions.keys())}

                            elif hasattr(part, 'text') and part.text:
                                has_text_response = True
                                # Clean text content by removing thinking tags
                                clean_text = re.sub(r'<thinking>.*?</thinking>', '', part.text, flags=re.DOTALL).strip()
                                text_content = clean_text

                        # Handle text responses (final analysis)
                        if has_text_response and not has_function_call:
                            update_progress("📝 Parsing final analysis with thinking insights...", 9, 10)

                            try:
                                result = json.loads(text_content)
                                # Add thinking insights and enhanced metadata
                                result["tool_usage"] = tool_usage_log
                                result["thinking_process"] = thinking_content.split('\n') if thinking_content else []
                                result["analysis_metadata"] = {
                                    "model_used": model_name,
                                    "thinking_enabled": current_thinking_config["enable_thinking"],
                                    "thinking_mode": thinking_mode,
                                    "total_tools_executed": len(tool_usage_log),
                                    "has_thinking_content": bool(thinking_content)
                                }
                                result["optimization_stats"] = {
                                    "total_tools_executed": len(tool_usage_log),
                                    "parallel_execution": True,
                                    "cached_results": sum(1 for log in tool_usage_log if log.get("cached", False))
                                }
                                update_progress("✅ Consolidated analysis completed!", 10, 10)
                                return result
                            except json.JSONDecodeError:
                                # Try to extract JSON from text
                                json_match = re.search(r'\{.*\}', text_content, re.DOTALL)
                                if json_match:
                                    try:
                                        result = json.loads(json_match.group())
                                        result["tool_usage"] = tool_usage_log
                                        result["thinking_process"] = thinking_content.split('\n') if thinking_content else []
                                        return result
                                    except json.JSONDecodeError:
                                        pass

                                return {
                                    "error": "Could not parse JSON from response",
                                    "raw_response": text_content[:500] + "..." if len(text_content) > 500 else text_content,
                                    "thinking_content": thinking_content,
                                    "tool_usage": tool_usage_log
                                }

                        # If no function calls were found, we're done
                        if not has_function_call:
                            if iteration == 0:
                                update_progress("⚠️ No tool calls requested by AI in first response", 8, 10)
                                # Add warning about insufficient tool usage
                                if len(tool_usage_log) < 3:
                                    update_progress(f"🚨 WARNING: Only {len(tool_usage_log)} tools executed. Minimum 3 required for quality analysis!", 8, 10)
                            else:
                                update_progress("✅ AI completed tool execution phase", 8, 10)
                                # Validate tool execution quality
                                required_tools = ["detect_trading_symbol", "get_market_context_summary", "get_comprehensive_market_news"]
                                executed_tool_names = [tool['tool_name'] for tool in tool_usage_log]
                                missing_tools = [tool for tool in required_tools if tool not in executed_tool_names]

                                if missing_tools:
                                    update_progress(f"⚠️ Missing critical tools: {', '.join(missing_tools)}", 8, 10)
                                else:
                                    update_progress(f"✅ All {len(tool_usage_log)} required tools executed successfully", 8, 10)
                            break

                # AI should provide final JSON response automatically based on prompt instructions
                # No need for additional final prompt - the main prompt already specifies the required JSON format
                update_progress("⚠️ AI did not provide final analysis in expected format", 9, 10)

                update_progress("⚠️ Maximum iterations reached", 9, 10)
                return {"error": "Maximum iterations reached without valid response"}

            except Exception as e:
                error_msg = str(e).lower()

                # Check for quota/rate limit errors and try model fallback
                if "quota" in error_msg or "limit" in error_msg or "429" in error_msg:
                    # Try model fallback if we haven't tried all models yet
                    current_model_index = model_fallback_chain.index(model_name) if model_name in model_fallback_chain else 0
                    if current_model_index < len(model_fallback_chain) - 1:
                        fallback_model = model_fallback_chain[current_model_index + 1]
                        update_progress(f"⚠️ Quota exceeded for {model_name}, trying fallback model {fallback_model}...")
                        
                        # Reinitialize with fallback model
                        try:
                            model = genai.GenerativeModel(
                                model_name=fallback_model,
                                tools=tool_definitions
                            )
                            model_name = fallback_model  # Update for next retry
                            update_progress(f"✅ Switched to fallback model: {fallback_model}")
                            continue  # Retry with new model
                        except Exception as fallback_error:
                            update_progress(f"❌ Fallback model {fallback_model} also failed: {str(fallback_error)}")
                    
                    # If no more models to try, wait and continue
                    update_progress("⚠️ API quota exceeded, waiting 60s...")
                    time.sleep(60)
                    continue

                if attempt < max_retries - 1:
                    if "500" in error_msg or "internal" in error_msg or "server" in error_msg:
                        delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
                        update_progress(f"🔄 Server error, retrying in {delay:.1f}s... (attempt {attempt + 1}/{max_retries})")
                        time.sleep(delay)
                        continue

                # Final attempt failed
                update_progress(f"❌ Error after {max_retries} attempts: {str(e)}", 10, 10)
                return {
                    "error": f"API error after {max_retries} attempts: {str(e)}",
                    "suggestion": "Try with a smaller image or check your API key",
                    "tool_usage": tool_usage_log
                }

    except Exception as e:
        import traceback
        update_progress(f"❌ Unexpected error: {str(e)}", 10, 10)
        return {
            "error": f"Exception in consolidated analysis: {str(e)}",
            "traceback": traceback.format_exc(),
            "suggestion": "Try with a smaller image or check your API key",
            "tool_usage": tool_usage_log
        }


def extract_trading_levels_from_analysis(analysis_result: Dict[str, Any]) -> Dict[str, float]:
    """
    Extract trading levels from AI analysis for chart annotation.
    This replaces the regex-based approach with proper JSON parsing.
    """
    levels = {}
    
    try:
        # Handle different analysis result formats
        if isinstance(analysis_result, dict):
            # Look for trade ideas in structured format
            if "trade_ideas" in analysis_result and analysis_result["trade_ideas"]:
                trade_ideas = analysis_result["trade_ideas"]
                if isinstance(trade_ideas, list) and len(trade_ideas) > 0:
                    trade = trade_ideas[0]  # Use first trade idea
                    
                    # Extract levels from trade structure
                    if isinstance(trade, dict):
                        if "entry_price" in trade:
                            levels["entry"] = float(trade["entry_price"])
                        if "stop_loss" in trade:
                            levels["stop_loss"] = float(trade["stop_loss"])
                        if "take_profit" in trade:
                            levels["take_profit"] = float(trade["take_profit"])
                        
            # Look for trade setup in scalp format
            if "trade_setup" in analysis_result:
                setup = analysis_result["trade_setup"]
                if isinstance(setup, dict):
                    if "entry" in setup:
                        levels["entry"] = float(setup["entry"])
                    if "stop_loss" in setup:
                        levels["stop_loss"] = float(setup["stop_loss"])
                    if "target" in setup or "take_profit" in setup:
                        levels["take_profit"] = float(setup.get("target", setup.get("take_profit", 0)))
            
            # Look for support/resistance levels in analysis text
            analysis_text = ""
            if isinstance(analysis_result, dict):
                # Try different keys for analysis text
                analysis_text = (
                    analysis_result.get("detailed_report", "") or
                    analysis_result.get("analysis_notes", "") or
                    analysis_result.get("analysis", "") or
                    analysis_result.get("raw_response", "") or
                    str(analysis_result)
                )
            else:
                analysis_text = str(analysis_result)

            # Extract using improved regex patterns for Indian market prices
            import re
            price_patterns = {
                'support': [
                    r'support[:\s]*(?:level[:\s]*)?(?:at[:\s]*)?(\d{2,6}(?:,\d{3})*(?:\.\d{1,2})?)',
                    r'(\d{2,6}(?:,\d{3})*(?:\.\d{1,2})?)[:\s]*support',
                    r'support[:\s]*(\d{2,6}(?:,\d{3})*(?:\.\d{1,2})?)',
                ],
                'resistance': [
                    r'resistance[:\s]*(?:level[:\s]*)?(?:at[:\s]*)?(\d{2,6}(?:,\d{3})*(?:\.\d{1,2})?)',
                    r'(\d{2,6}(?:,\d{3})*(?:\.\d{1,2})?)[:\s]*resistance',
                    r'resistance[:\s]*(\d{2,6}(?:,\d{3})*(?:\.\d{1,2})?)',
                ],
                'entry': [
                    r'entry[:\s]*(?:point[:\s]*)?(?:at[:\s]*)?(\d{2,6}(?:,\d{3})*(?:\.\d{1,2})?)',
                    r'buy[:\s]*(?:at[:\s]*)?(\d{2,6}(?:,\d{3})*(?:\.\d{1,2})?)',
                    r'sell[:\s]*(?:at[:\s]*)?(\d{2,6}(?:,\d{3})*(?:\.\d{1,2})?)',
                    r'enter[:\s]*(?:at[:\s]*)?(\d{2,6}(?:,\d{3})*(?:\.\d{1,2})?)',
                ],
                'stop_loss': [
                    r'stop[:\s]*loss[:\s]*(?:at[:\s]*)?(\d{2,6}(?:,\d{3})*(?:\.\d{1,2})?)',
                    r'sl[:\s]*(?:at[:\s]*)?(\d{2,6}(?:,\d{3})*(?:\.\d{1,2})?)',
                    r'stop[:\s]*(?:at[:\s]*)?(\d{2,6}(?:,\d{3})*(?:\.\d{1,2})?)',
                ],
                'take_profit': [
                    r'take[:\s]*profit[:\s]*(?:at[:\s]*)?(\d{2,6}(?:,\d{3})*(?:\.\d{1,2})?)',
                    r'tp[:\s]*(?:at[:\s]*)?(\d{2,6}(?:,\d{3})*(?:\.\d{1,2})?)',
                    r'target[:\s]*(?:at[:\s]*)?(\d{2,6}(?:,\d{3})*(?:\.\d{1,2})?)',
                    r'profit[:\s]*(?:at[:\s]*)?(\d{2,6}(?:,\d{3})*(?:\.\d{1,2})?)',
                ]
            }

            text_lower = analysis_text.lower()
            for level_type, patterns in price_patterns.items():
                if level_type not in levels:  # Only if not already found in structured data
                    for pattern in patterns:
                        matches = re.findall(pattern, text_lower)
                        if matches:
                            try:
                                # Handle comma-separated numbers (e.g., "55,600")
                                price_str = matches[0].replace(',', '')
                                price_value = float(price_str)

                                # Validate price range (for Indian markets, typically 100-100000)
                                if 100 <= price_value <= 100000:
                                    levels[level_type] = price_value
                                    break
                            except (ValueError, IndexError):
                                continue

            # Special handling for ranges (e.g., "55,950-56,050")
            range_patterns = [
                r'(\d{2,6}(?:,\d{3})*(?:\.\d{1,2})?)\s*-\s*(\d{2,6}(?:,\d{3})*(?:\.\d{1,2})?)',
                r'between\s*(\d{2,6}(?:,\d{3})*(?:\.\d{1,2})?)\s*(?:and|to)\s*(\d{2,6}(?:,\d{3})*(?:\.\d{1,2})?)',
            ]

            for pattern in range_patterns:
                matches = re.findall(pattern, text_lower)
                if matches:
                    try:
                        low_price = float(matches[0][0].replace(',', ''))
                        high_price = float(matches[0][1].replace(',', ''))

                        # Use the range midpoint if no specific levels found
                        if 'entry' not in levels and 'resistance' not in levels:
                            levels['entry'] = (low_price + high_price) / 2
                        if 'resistance' not in levels:
                            levels['resistance'] = high_price
                        if 'support' not in levels:
                            levels['support'] = low_price
                        break
                    except (ValueError, IndexError):
                        continue
        
        # Log found levels for debugging
        if levels:
            logger.info(f"Extracted trading levels: {levels}")
        else:
            logger.warning("No trading levels found in analysis")
            
    except Exception as e:
        logger.error(f"Error extracting trading levels: {e}")
    
    return levels


def create_enhanced_chart_annotation(analysis_result: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create enhanced annotation data for charts including levels and signals.
    """
    try:
        # Extract trading levels
        levels = extract_trading_levels_from_analysis(analysis_result)
        
        # Determine trade direction
        trade_direction = "neutral"
        if "trade_ideas" in analysis_result and analysis_result["trade_ideas"]:
            first_trade = analysis_result["trade_ideas"][0]
            if isinstance(first_trade, dict) and "direction" in first_trade:
                trade_direction = first_trade["direction"].lower()
        
        # Extract confidence/signal strength
        confidence = 0.5  # Default
        if "trade_setup" in analysis_result:
            setup = analysis_result["trade_setup"]
            if isinstance(setup, dict) and "confidence" in setup:
                confidence = float(setup["confidence"]) / 100.0
        
        annotation_data = {
            "levels": levels,
            "trade_direction": trade_direction,
            "confidence": confidence,
            "has_valid_setup": len(levels) >= 2,  # At least entry and one target
            "annotation_timestamp": datetime.now().isoformat()
        }
        
        return annotation_data
        
    except Exception as e:
        logger.error(f"Error creating chart annotation data: {e}")
        return {
            "levels": {},
            "trade_direction": "neutral",
            "confidence": 0.5,
            "has_valid_setup": False,
            "error": str(e)
        }


def generate_analysis_cloud(images: List[Image.Image], prompt: str, api_key: str, model_name: str, progress_callback=None) -> dict:
    """Send images and prompt to Gemini with enhanced error handling, retry logic, and progress tracking."""

    def update_progress(message: str, step: int = None, total_steps: int = None):
        """Update progress if callback is provided."""
        if progress_callback:
            progress_callback(message, step, total_steps)

    try:
        update_progress("🔧 Initializing analysis system...", 1, 10)

        # Validate and compress images
        processed_images = []
        for i, img in enumerate(images):
            update_progress(f"🖼️ Processing image {i+1}/{len(images)}...", 2, 10)
            compressed_img = compress_image_if_needed(img)
            processed_images.append(compressed_img)

        # Validate request size
        if not validate_request_size(processed_images, prompt):
            return {"error": "Request size too large after compression"}

        update_progress("🔑 Configuring API...", 3, 10)
        genai.configure(api_key=api_key)

        # Use the enhanced tool registry
        registry = get_tool_registry()
        tool_usage_log = []
        tool_definitions = registry.get_tool_definitions()
        tool_names = registry.get_tool_names()

        # Create dynamic mapping for all registered tools
        tool_functions = {}
        for tool_name in tool_names:
            # Use closure to capture tool_name correctly
            tool_functions[tool_name] = (lambda name: lambda **kwargs: registry.execute_tool(name, **kwargs).data)(tool_name)

        # Retry logic with exponential backoff
        max_retries = 3
        base_delay = 1.0

        for attempt in range(max_retries):
            try:
                update_progress(f"🤖 Creating AI model (attempt {attempt + 1}/{max_retries})...", 4, 10)

                # Create model with tools
                model = genai.GenerativeModel(
                    model_name=model_name,
                    tools=tool_definitions
                )

                update_progress("💬 Starting analysis conversation...", 5, 10)

                # Prepare content
                content_parts = [prompt] + processed_images

                update_progress("📊 Sending chart for analysis...", 6, 10)
                chat = model.start_chat()
                response = chat.send_message(content_parts)
                break  # Success, exit retry loop

            except Exception as e:
                error_msg = str(e)

                # Check for specific error types
                if "500" in error_msg or "Internal" in error_msg:
                    if attempt < max_retries - 1:
                        delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
                        update_progress(f"⚠️ Server error, retrying in {delay:.1f}s...", 4, 10)
                        time.sleep(delay)
                        continue
                    else:
                        return {
                            "error": f"Server error after {max_retries} attempts: {error_msg}",
                            "suggestion": "Try again in a few minutes or use a smaller image"
                        }
                elif "quota" in error_msg.lower() or "limit" in error_msg.lower():
                    return {
                        "error": f"API quota/rate limit exceeded: {error_msg}",
                        "suggestion": "Wait a few minutes before trying again"
                    }
                else:
                    return {"error": f"API error: {error_msg}"}
        else:
            return {"error": "Failed to connect after all retry attempts"}

        update_progress("🔧 Processing AI response and tool calls...", 7, 10)

        # Handle tool calls and responses
        max_iterations = 8  # Increased to allow for comprehensive analysis
        for iteration in range(max_iterations):

            if iteration > 0:
                update_progress(f"🔄 Processing iteration {iteration + 1}/{max_iterations}...", 7 + iteration, 10 + max_iterations)

            # Check if response is valid
            if not response or not response.candidates or not response.candidates[0].content:
                return {"error": "Invalid response from AI", "iteration": iteration}

            content = response.candidates[0].content
            if not content.parts:
                return {"error": "No content parts in response", "iteration": iteration}

            # Look for function calls or text in response parts
            has_function_call = False

            for part in content.parts:
                # Handle function calls
                if hasattr(part, 'function_call') and part.function_call:
                    has_function_call = True
                    func_call = part.function_call
                    func_name = func_call.name

                    # Validate function exists
                    if func_name not in tool_functions:
                        return {"error": f"Unknown function: {func_name}"}

                    try:
                        # Execute function with progress tracking
                        update_progress(f"🔧 Executing tool: {func_name}...", 8, 10)
                        func_args = dict(func_call.args)
                        result = tool_functions[func_name](**func_args)

                        # Use Gemini 2.5 Flash for intelligent summarization
                        from .parallel_utils import summarize_tool_result
                        result_summary = summarize_tool_result(func_name, result, use_llm_summary=True)

                        # Log tool usage with intelligent summary
                        tool_usage_log.append({
                            "tool_name": func_name,
                            "arguments": func_args,
                            "result_summary": result_summary,
                            "timestamp": datetime.now().isoformat()
                        })

                        update_progress(f"✅ Tool {func_name} completed successfully", 8, 10)

                        # Send result back to model
                        function_response = genai.protos.FunctionResponse(
                            name=func_name,
                            response={"result": result}
                        )
                        response = chat.send_message(
                            genai.protos.Part(function_response=function_response)
                        )
                        break  # Process new response

                    except Exception as e:
                        return {"error": f"Function {func_name} failed: {str(e)}"}

                # Handle text responses
                elif hasattr(part, 'text') and part.text:
                    update_progress("📝 Parsing final analysis...", 9, 10)
                    text_content = part.text.strip()

                    # Try to parse as JSON
                    try:
                        result = json.loads(text_content)
                        # Add tool usage information to the result
                        result["tool_usage"] = tool_usage_log
                        update_progress("✅ Analysis completed successfully!", 10, 10)
                        return result
                    except json.JSONDecodeError:
                        # Try to extract JSON from markdown code blocks first
                        import re

                        # Look for JSON in markdown code blocks
                        markdown_json_pattern = r'```(?:json)?\s*(\{.*?\})\s*```'
                        markdown_matches = re.findall(markdown_json_pattern, text_content, re.DOTALL)

                        for match in markdown_matches:
                            try:
                                result = json.loads(match)
                                result["tool_usage"] = tool_usage_log
                                return result
                            except json.JSONDecodeError:
                                continue

                        # Fallback to original JSON extraction
                        json_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
                        matches = re.findall(json_pattern, text_content, re.DOTALL)

                        for match in matches:
                            try:
                                result = json.loads(match)
                                result["tool_usage"] = tool_usage_log
                                return result
                            except json.JSONDecodeError:
                                continue

                        # If no valid JSON found, return error with raw text
                        return {
                            "error": "Could not parse JSON from response",
                            "raw_response": text_content
                        }

            # If no function calls were found, we're done
            if not has_function_call:
                break

        update_progress("⚠️ Maximum iterations reached", 10, 10)
        return {"error": "Maximum iterations reached without valid response"}

    except Exception as e:
        import traceback
        update_progress(f"❌ Error occurred: {str(e)}", 10, 10)
        return {
            "error": f"Exception in generate_analysis_cloud: {str(e)}",
            "traceback": traceback.format_exc(),
            "suggestion": "Try with a smaller image or check your API key"
        }


def generate_analysis_local(images: List[Image.Image], prompt: str, model_name: str) -> dict:
    """Send images and prompt to a local Ollama model and return the JSON response."""
    if not ollama:
        raise RuntimeError("Ollama package not available")

    images_b64 = encode_images_to_base64(images)
    full_response = ollama.chat(
        model=model_name,
        messages=[{"role": "user", "content": prompt, "images": images_b64}],
        format="json",
    )
    # Ollama's tool calling is different; this assumes it returns the final JSON directly
    # after internal tool execution if 'format="json"' is used and the model supports it.
    # Parse the JSON string from the response before returning
    return json.loads(full_response["message"]["content"])


def get_recent_data(symbol: str, period: str = "1mo", interval: str = "1d"):
    """Fetch recent historical data for a symbol using ``yfinance``.

    Parameters
    ----------
    symbol : str
        Ticker symbol to download, e.g., ``"AAPL"``.
    period : str, optional
        Data period to download (default ``"1mo"``). Any value accepted by
        ``yfinance`` such as ``"1d"``, ``"5d"``, ``"1mo"``, ``"3mo"``.
    interval : str, optional
        Data interval (default ``"1d"``). Examples include ``"1d"``, ``"1h"``.

    Returns
    -------
    pandas.DataFrame
        DataFrame containing OHLCV data indexed by datetime. The DataFrame will
        be empty if no data is found or an error occurs.
    """
    try:
        # Suppress warnings and stderr to avoid cluttering the output
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            with redirect_stderr(io.StringIO()):
                ticker = yf.Ticker(symbol)
                data = ticker.history(period=period, interval=interval)
                return data if isinstance(data, pd.DataFrame) else pd.DataFrame()
    except Exception:
        # Swallow errors and return empty DataFrame to keep UI responsive
        return pd.DataFrame()
