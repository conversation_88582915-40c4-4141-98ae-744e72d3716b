"""
Optimized Trading Analysis Utils with Parallel Execution and DuckDuckGo News
Reduces analysis time from 184s to ~30-45s
"""

import logging
import time
import json
import re
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime

# Import optimized modules
from .parallel_executor import trading_executor
from .duckduckgo_news import duckduckgo_news
from .prompts import positional_prompt, scalp_prompt, crypto_prompt, indian_market_prompt

import google.generativeai as genai

logger = logging.getLogger(__name__)

def generate_analysis_optimized(
    processed_images: List[Any],
    prompt_type: str = "positional",
    model_name: str = "gemini-2.0-flash-exp",
    progress_callback: Optional[Callable] = None,
    symbol: Optional[str] = None
) -> Dict[str, Any]:
    """
    Optimized analysis with parallel tool execution and DuckDuckGo news
    
    Args:
        processed_images: List of processed chart images
        prompt_type: Type of analysis ("positional", "scalp", "crypto", "indian")
        model_name: Gemini model to use
        progress_callback: Progress update callback
        symbol: Trading symbol for analysis
        
    Returns:
        Analysis results with tool data and AI response
    """
    start_time = time.time()
    
    def update_progress(message: str, current: int = 1, total: int = 6):
        if progress_callback:
            progress_callback(message, current, total)
        logger.info(message)
    
    try:
        update_progress("🚀 Starting optimized analysis...", 1, 6)
        
        # Step 1: Select appropriate prompt
        prompt_map = {
            "positional": positional_prompt,
            "scalp": scalp_prompt,
            "crypto": crypto_prompt,
            "indian": indian_market_prompt
        }
        
        selected_prompt = prompt_map.get(prompt_type, positional_prompt)
        update_progress(f"📋 Using {prompt_type} analysis prompt", 2, 6)
        
        # Step 2: Execute tools in parallel (if symbol provided)
        tool_results = {}
        if symbol:
            update_progress("⚡ Executing tools in parallel...", 3, 6)
            
            if prompt_type == "crypto":
                tool_results = trading_executor.execute_crypto_analysis(symbol, progress_callback)
            elif prompt_type == "indian":
                tool_results = trading_executor.execute_indian_market_analysis(symbol, progress_callback)
            else:
                tool_results = trading_executor.execute_general_analysis(symbol, progress_callback)
        else:
            update_progress("⏭️ Skipping tool execution (no symbol provided)", 3, 6)
        
        # Step 3: Initialize Gemini model
        update_progress("🤖 Initializing Gemini model...", 4, 6)
        
        try:
            model = genai.GenerativeModel(model_name=model_name)
        except Exception as e:
            logger.warning(f"Failed to initialize {model_name}, falling back to gemini-1.5-pro")
            model = genai.GenerativeModel(model_name="gemini-1.5-pro")
            model_name = "gemini-1.5-pro"
        
        # Step 4: Prepare content with tool data summary
        update_progress("📊 Preparing analysis content...", 5, 6)
        
        content_parts = []
        
        # Add tool data summary to prompt if available
        if tool_results:
            tool_summary = _create_tool_summary(tool_results)
            enhanced_prompt = f"""
{selected_prompt}

**TOOL DATA AVAILABLE:**
{tool_summary}

Use this tool data to enhance your analysis and validate chart levels.
"""
            content_parts.append(enhanced_prompt)
        else:
            content_parts.append(selected_prompt)
        
        # Add chart images
        for img in processed_images:
            content_parts.append(img)
        
        # Step 5: Generate analysis
        update_progress("🧠 Generating AI analysis...", 6, 6)
        
        try:
            response = model.generate_content(content_parts)
            
            if response.candidates and response.candidates[0].content.parts:
                response_text = ""
                for part in response.candidates[0].content.parts:
                    if hasattr(part, 'text') and part.text:
                        response_text += part.text
                
                # Try to parse JSON response
                analysis_result = _parse_analysis_response(response_text)
                
                # Add metadata
                analysis_result.update({
                    "tool_results": tool_results,
                    "analysis_metadata": {
                        "model_used": model_name,
                        "prompt_type": prompt_type,
                        "symbol": symbol,
                        "execution_time": time.time() - start_time,
                        "tools_executed": len(tool_results),
                        "timestamp": datetime.now().isoformat()
                    }
                })
                
                update_progress("✅ Analysis completed successfully!", 6, 6)
                return analysis_result
                
            else:
                raise Exception("No valid response from Gemini model")
                
        except Exception as e:
            logger.error(f"Error generating analysis: {str(e)}")
            return {
                "error": f"Analysis generation failed: {str(e)}",
                "tool_results": tool_results,
                "execution_time": time.time() - start_time
            }
    
    except Exception as e:
        logger.error(f"Optimized analysis failed: {str(e)}")
        return {
            "error": f"Optimized analysis failed: {str(e)}",
            "execution_time": time.time() - start_time
        }

def _create_tool_summary(tool_results: Dict[str, Any]) -> str:
    """Create a concise summary of tool results for the AI"""
    summary_parts = []
    
    for tool_name, result in tool_results.items():
        if result.success:
            if tool_name == "get_market_news":
                news_data = result.data
                if news_data.get("success") and news_data.get("news_articles"):
                    summary_parts.append(f"📰 **Market News ({len(news_data['news_articles'])} articles):**")
                    for article in news_data["news_articles"][:3]:  # Top 3 articles
                        summary_parts.append(f"- {article['title']} ({article['source']})")
            
            elif tool_name == "get_fii_dii_news":
                fii_data = result.data
                if fii_data.get("success") and fii_data.get("fii_dii_news"):
                    summary_parts.append(f"💰 **FII/DII News ({len(fii_data['fii_dii_news'])} articles):**")
                    for article in fii_data["fii_dii_news"][:2]:  # Top 2 articles
                        summary_parts.append(f"- {article['title']}")
            
            elif tool_name == "get_market_data":
                market_data = result.data
                if market_data and isinstance(market_data, dict):
                    summary_parts.append(f"📈 **Market Data:** Current price and volume data available")
            
            elif tool_name == "detect_trading_symbol":
                symbol_data = result.data
                if symbol_data:
                    summary_parts.append(f"🎯 **Symbol Detected:** {symbol_data}")
        
        else:
            summary_parts.append(f"❌ **{tool_name}:** {result.error}")
    
    return "\n".join(summary_parts) if summary_parts else "No tool data available"

def _parse_analysis_response(response_text: str) -> Dict[str, Any]:
    """Parse AI response and extract JSON if present"""
    
    # Try to find JSON in the response
    json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
    
    if json_match:
        try:
            json_data = json.loads(json_match.group())
            return json_data
        except json.JSONDecodeError:
            pass
    
    # If no valid JSON found, return structured response
    return {
        "status": "Analysis Complete",
        "analysis_summary": "Analysis completed but not in expected JSON format",
        "detailed_report": response_text,
        "trade_ideas": [],
        "key_levels": {"support": [], "resistance": []},
        "market_context": "See detailed report for analysis",
        "risk_management": "Use proper risk management principles",
        "raw_response": response_text
    }

# Backward compatibility function
def generate_analysis_cloud_optimized(*args, **kwargs):
    """Backward compatibility wrapper for the old function name"""
    return generate_analysis_optimized(*args, **kwargs)
