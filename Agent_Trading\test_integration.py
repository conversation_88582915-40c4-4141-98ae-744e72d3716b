#!/usr/bin/env python3
"""
Integration Test for Trading Analysis AI
Tests all major components and their integration
"""

import sys
import os
import io
from PIL import Image
import json

# Add project root to path
sys.path.insert(0, os.path.dirname(__file__))

def test_imports():
    """Test that all modules can be imported without errors"""
    print("🔍 Testing imports...")
    
    try:
        # Core modules
        from helpers.prompts import (
            positional_prompt, scalp_prompt, crypto_prompt, 
            indian_market_prompt, tool_summarization_prompts
        )
        print("✅ Prompts module imported successfully")
        
        # gemini_2_5_integration removed - using existing utils.py functions
        from helpers.utils import generate_analysis_cloud_optimized
        print("✅ Existing Gemini integration (utils.py) imported successfully")
        
        from helpers.chart_annotator import chart_annotator
        print("✅ Chart annotator imported successfully")
        
        from helpers.api_optimizer import api_optimizer
        print("✅ API optimizer imported successfully")
        
        from helpers.image_analyzer import advanced_image_analyzer
        print("✅ Advanced image analyzer imported successfully")
        
        from helpers.enhanced_news_sources import enhanced_news_extractor
        print("✅ Enhanced news sources imported successfully")
        
        from helpers.quota_manager import quota_manager
        print("✅ Quota manager imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_prompts_structure():
    """Test that prompts are properly structured"""
    print("\n🔍 Testing prompts structure...")
    
    try:
        from helpers.prompts import tool_summarization_prompts, generic_summarization_prompt
        
        # Check tool summarization prompts
        expected_tools = [
            "get_market_context_summary",
            "get_comprehensive_market_news", 
            "get_fii_dii_flows",
            "get_technical_analysis"
        ]
        
        for tool in expected_tools:
            if tool not in tool_summarization_prompts:
                print(f"❌ Missing prompt for tool: {tool}")
                return False
            
            if "{data}" not in tool_summarization_prompts[tool]:
                print(f"❌ Prompt for {tool} missing {{data}} placeholder")
                return False
        
        # Check generic prompt
        if "{tool_name}" not in generic_summarization_prompt or "{data}" not in generic_summarization_prompt:
            print("❌ Generic prompt missing required placeholders")
            return False
        
        print("✅ All prompts properly structured")
        return True
        
    except Exception as e:
        print(f"❌ Prompts test failed: {e}")
        return False

def test_chart_annotator():
    """Test chart annotator functionality"""
    print("\n🔍 Testing chart annotator...")
    
    try:
        from helpers.chart_annotator import chart_annotator
        
        # Create a simple test image
        test_image = Image.new('RGB', (800, 600), color='white')
        img_buffer = io.BytesIO()
        test_image.save(img_buffer, format='JPEG')
        img_bytes = img_buffer.getvalue()
        
        # Test analysis result with trading levels
        test_analysis = {
            'answer': 'Entry at 25000, stop loss at 24800, take profit at 25500, support at 24900, resistance at 25200',
            'success': True
        }
        
        # Test annotation
        annotated_bytes = chart_annotator.annotate_chart(img_bytes, test_analysis)
        
        if annotated_bytes and len(annotated_bytes) > 0:
            print("✅ Chart annotator working correctly")
            return True
        else:
            print("❌ Chart annotator returned empty result")
            return False
            
    except Exception as e:
        print(f"❌ Chart annotator test failed: {e}")
        return False

def test_api_optimizer():
    """Test API optimizer functionality"""
    print("\n🔍 Testing API optimizer...")
    
    try:
        from helpers.api_optimizer import api_optimizer
        
        # Test optimization request
        optimization = api_optimizer.optimize_request(
            request_type="chart_analysis",
            content="Test analysis request",
            model="gemini-2.5-flash"
        )
        
        required_keys = ['use_cache', 'cache_key', 'recommended_model', 'thinking_budget', 'batch_eligible']
        for key in required_keys:
            if key not in optimization:
                print(f"❌ API optimizer missing key: {key}")
                return False
        
        # Test stats
        stats = api_optimizer.get_optimization_stats()
        if not isinstance(stats, dict):
            print("❌ API optimizer stats not returning dict")
            return False
        
        print("✅ API optimizer working correctly")
        return True
        
    except Exception as e:
        print(f"❌ API optimizer test failed: {e}")
        return False

def test_image_analyzer():
    """Test advanced image analyzer"""
    print("\n🔍 Testing advanced image analyzer...")
    
    try:
        from helpers.image_analyzer import advanced_image_analyzer
        
        # Create a test image
        test_image = Image.new('RGB', (800, 600), color='white')
        img_buffer = io.BytesIO()
        test_image.save(img_buffer, format='JPEG')
        img_bytes = img_buffer.getvalue()
        
        # Test chart structure analysis
        analysis = advanced_image_analyzer.analyze_chart_structure(img_bytes)
        
        required_keys = ['chart_type', 'timeframe', 'price_scale', 'volume_present', 'indicators', 'analysis_confidence']
        for key in required_keys:
            if key not in analysis:
                print(f"❌ Image analyzer missing key: {key}")
                return False
        
        print("✅ Advanced image analyzer working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Image analyzer test failed: {e}")
        return False

def test_quota_manager():
    """Test quota manager functionality"""
    print("\n🔍 Testing quota manager...")
    
    try:
        from helpers.quota_manager import quota_manager
        
        # Test quota status
        status = quota_manager.get_quota_status()
        
        required_keys = ['remaining', 'daily_quota', 'reset_time']
        for key in required_keys:
            if key not in status:
                print(f"❌ Quota manager missing key: {key}")
                return False
        
        print("✅ Quota manager working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Quota manager test failed: {e}")
        return False

def test_config_loading():
    """Test configuration loading"""
    print("\n🔍 Testing configuration loading...")
    
    try:
        config_path = os.path.join(os.path.dirname(__file__), 'config.json')
        
        if not os.path.exists(config_path):
            print("⚠️ config.json not found - this is expected for new setups")
            return True
        
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        if 'google_api_key' not in config:
            print("⚠️ google_api_key not found in config.json")
            return True
        
        print("✅ Configuration loaded successfully")
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def main():
    """Run all integration tests"""
    print("🚀 Starting Trading Analysis AI Integration Tests\n")
    
    tests = [
        test_imports,
        test_prompts_structure,
        test_chart_annotator,
        test_api_optimizer,
        test_image_analyzer,
        test_quota_manager,
        test_config_loading
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The system is ready to use.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
