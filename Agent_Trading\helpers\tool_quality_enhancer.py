"""
Tool Quality Enhancement System
Ensures tools provide high-quality, relevant data for better analysis
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import json

logger = logging.getLogger(__name__)

class ToolQualityEnhancer:
    """Enhances tool data quality and relevance for better analysis results."""
    
    def __init__(self):
        self.quality_thresholds = {
            'min_data_points': 10,
            'max_age_hours': 24,
            'min_relevance_score': 0.7,
            'required_fields': {
                'market_data': ['price', 'volume', 'timestamp'],
                'news_data': ['title', 'summary', 'timestamp'],
                'technical_data': ['signal', 'confidence', 'indicators']
            }
        }
        
        self.enhancement_stats = {
            'tools_enhanced': 0,
            'data_quality_improved': 0,
            'relevance_filtered': 0
        }
    
    def enhance_tool_result(self, tool_name: str, result: Any, context: Dict = None) -> Dict[str, Any]:
        """
        Enhance tool result quality and relevance.
        
        Args:
            tool_name: Name of the tool
            result: Raw tool result
            context: Additional context (symbol, timeframe, etc.)
            
        Returns:
            Enhanced result with quality metrics
        """
        try:
            enhanced_result = {
                'original_result': result,
                'enhanced_data': None,
                'quality_score': 0.0,
                'relevance_score': 0.0,
                'enhancement_applied': False,
                'quality_issues': [],
                'recommendations': []
            }
            
            # Apply tool-specific enhancements
            if 'market' in tool_name.lower():
                enhanced_result = self._enhance_market_data(result, enhanced_result, context)
            elif 'news' in tool_name.lower():
                enhanced_result = self._enhance_news_data(result, enhanced_result, context)
            elif 'technical' in tool_name.lower():
                enhanced_result = self._enhance_technical_data(result, enhanced_result, context)
            elif 'flow' in tool_name.lower():
                enhanced_result = self._enhance_flow_data(result, enhanced_result, context)
            else:
                enhanced_result = self._enhance_generic_data(result, enhanced_result, context)
            
            # Calculate overall quality score
            enhanced_result['quality_score'] = self._calculate_quality_score(enhanced_result)
            
            # Update stats
            if enhanced_result['enhancement_applied']:
                self.enhancement_stats['tools_enhanced'] += 1
                
            return enhanced_result
            
        except Exception as e:
            logger.error(f"Tool enhancement failed for {tool_name}: {e}")
            return {
                'original_result': result,
                'enhanced_data': result,
                'quality_score': 0.5,
                'relevance_score': 0.5,
                'enhancement_applied': False,
                'quality_issues': [f"Enhancement failed: {str(e)}"],
                'recommendations': ["Use original data with caution"]
            }
    
    def _enhance_market_data(self, result: Any, enhanced_result: Dict, context: Dict = None) -> Dict:
        """Enhance market data quality."""
        try:
            if not isinstance(result, dict):
                enhanced_result['quality_issues'].append("Market data not in expected format")
                return enhanced_result
            
            enhanced_data = result.copy()
            quality_issues = []
            recommendations = []
            
            # Check for required fields
            required_fields = self.quality_thresholds['required_fields']['market_data']
            missing_fields = [field for field in required_fields if field not in result]
            
            if missing_fields:
                quality_issues.append(f"Missing required fields: {missing_fields}")
                recommendations.append("Ensure data source provides complete market data")
            
            # Validate price data
            if 'price' in result:
                price = result['price']
                if isinstance(price, (int, float)) and price > 0:
                    enhanced_data['price_validated'] = True
                else:
                    quality_issues.append("Invalid price data")
                    recommendations.append("Verify price data source")
            
            # Validate volume data
            if 'volume' in result:
                volume = result['volume']
                if isinstance(volume, (int, float)) and volume >= 0:
                    enhanced_data['volume_validated'] = True
                else:
                    quality_issues.append("Invalid volume data")
            
            # Check data freshness
            if 'timestamp' in result:
                try:
                    timestamp = datetime.fromisoformat(str(result['timestamp']).replace('Z', '+00:00'))
                    age_hours = (datetime.now() - timestamp).total_seconds() / 3600
                    
                    if age_hours > self.quality_thresholds['max_age_hours']:
                        quality_issues.append(f"Data is {age_hours:.1f} hours old")
                        recommendations.append("Use more recent data for better accuracy")
                    else:
                        enhanced_data['data_freshness'] = 'good'
                except:
                    quality_issues.append("Invalid timestamp format")
            
            # Add market context if symbol is provided
            if context and 'symbol' in context:
                symbol = context['symbol'].upper()
                enhanced_data['market_type'] = self._identify_market_type(symbol)
                enhanced_data['trading_hours'] = self._get_trading_hours(symbol)
            
            enhanced_result['enhanced_data'] = enhanced_data
            enhanced_result['quality_issues'] = quality_issues
            enhanced_result['recommendations'] = recommendations
            enhanced_result['enhancement_applied'] = True
            
            return enhanced_result
            
        except Exception as e:
            logger.error(f"Market data enhancement failed: {e}")
            enhanced_result['quality_issues'].append(f"Enhancement error: {str(e)}")
            return enhanced_result
    
    def _enhance_news_data(self, result: Any, enhanced_result: Dict, context: Dict = None) -> Dict:
        """Enhance news data quality and relevance."""
        try:
            if not isinstance(result, dict) or 'news' not in result:
                enhanced_result['quality_issues'].append("News data not in expected format")
                return enhanced_result
            
            news_items = result['news']
            if not isinstance(news_items, list):
                enhanced_result['quality_issues'].append("News items not in list format")
                return enhanced_result
            
            enhanced_news = []
            quality_issues = []
            recommendations = []
            
            # Filter and enhance news items
            for item in news_items:
                if not isinstance(item, dict):
                    continue
                
                enhanced_item = item.copy()
                
                # Check for required fields
                required_fields = self.quality_thresholds['required_fields']['news_data']
                if all(field in item for field in required_fields):
                    enhanced_item['completeness_score'] = 1.0
                else:
                    missing = [f for f in required_fields if f not in item]
                    enhanced_item['completeness_score'] = 1.0 - (len(missing) / len(required_fields))
                
                # Calculate relevance score based on context
                if context and 'symbol' in context:
                    relevance_score = self._calculate_news_relevance(item, context['symbol'])
                    enhanced_item['relevance_score'] = relevance_score
                    
                    # Only include highly relevant news
                    if relevance_score >= self.quality_thresholds['min_relevance_score']:
                        enhanced_news.append(enhanced_item)
                else:
                    enhanced_news.append(enhanced_item)
            
            # Sort by relevance and recency
            enhanced_news.sort(key=lambda x: (
                x.get('relevance_score', 0.5),
                x.get('timestamp', '2000-01-01')
            ), reverse=True)
            
            # Limit to top 10 most relevant news items
            enhanced_news = enhanced_news[:10]
            
            if len(enhanced_news) < len(news_items):
                quality_issues.append(f"Filtered {len(news_items) - len(enhanced_news)} low-relevance news items")
                recommendations.append("Focus on high-relevance news for better analysis")
            
            enhanced_result['enhanced_data'] = {
                **result,
                'news': enhanced_news,
                'total_filtered': len(news_items) - len(enhanced_news)
            }
            enhanced_result['quality_issues'] = quality_issues
            enhanced_result['recommendations'] = recommendations
            enhanced_result['enhancement_applied'] = True
            
            return enhanced_result
            
        except Exception as e:
            logger.error(f"News data enhancement failed: {e}")
            enhanced_result['quality_issues'].append(f"Enhancement error: {str(e)}")
            return enhanced_result
    
    def _enhance_technical_data(self, result: Any, enhanced_result: Dict, context: Dict = None) -> Dict:
        """Enhance technical analysis data."""
        try:
            enhanced_data = result.copy() if isinstance(result, dict) else {'raw_data': result}
            quality_issues = []
            recommendations = []
            
            # Validate technical indicators
            if 'indicators' in result:
                indicators = result['indicators']
                if isinstance(indicators, dict):
                    # Check indicator confidence levels
                    for indicator, value in indicators.items():
                        if isinstance(value, dict) and 'confidence' in value:
                            confidence = value['confidence']
                            if confidence < 0.6:
                                quality_issues.append(f"Low confidence in {indicator}: {confidence}")
                                recommendations.append(f"Consider additional confirmation for {indicator}")
                
            # Validate signals
            if 'signal' in result:
                signal = result['signal']
                if signal not in ['BUY', 'SELL', 'HOLD', 'NEUTRAL']:
                    quality_issues.append(f"Unexpected signal format: {signal}")
                    recommendations.append("Standardize signal format")
            
            enhanced_result['enhanced_data'] = enhanced_data
            enhanced_result['quality_issues'] = quality_issues
            enhanced_result['recommendations'] = recommendations
            enhanced_result['enhancement_applied'] = True
            
            return enhanced_result
            
        except Exception as e:
            logger.error(f"Technical data enhancement failed: {e}")
            enhanced_result['quality_issues'].append(f"Enhancement error: {str(e)}")
            return enhanced_result
    
    def _enhance_flow_data(self, result: Any, enhanced_result: Dict, context: Dict = None) -> Dict:
        """Enhance FII/DII flow data."""
        try:
            enhanced_data = result.copy() if isinstance(result, dict) else {'raw_data': result}
            quality_issues = []
            recommendations = []
            
            # Validate flow amounts
            flow_fields = ['fii_flow', 'dii_flow', 'net_flow']
            for field in flow_fields:
                if field in result:
                    value = result[field]
                    if not isinstance(value, (int, float)):
                        quality_issues.append(f"Invalid {field} format")
                    elif abs(value) > 10000:  # Crores
                        enhanced_data[f'{field}_magnitude'] = 'high'
                    elif abs(value) > 1000:
                        enhanced_data[f'{field}_magnitude'] = 'medium'
                    else:
                        enhanced_data[f'{field}_magnitude'] = 'low'
            
            enhanced_result['enhanced_data'] = enhanced_data
            enhanced_result['quality_issues'] = quality_issues
            enhanced_result['recommendations'] = recommendations
            enhanced_result['enhancement_applied'] = True
            
            return enhanced_result
            
        except Exception as e:
            logger.error(f"Flow data enhancement failed: {e}")
            enhanced_result['quality_issues'].append(f"Enhancement error: {str(e)}")
            return enhanced_result
    
    def _enhance_generic_data(self, result: Any, enhanced_result: Dict, context: Dict = None) -> Dict:
        """Enhance generic tool data."""
        enhanced_data = result
        quality_issues = []
        
        # Basic data validation
        if result is None:
            quality_issues.append("Tool returned null result")
        elif isinstance(result, str) and len(result.strip()) == 0:
            quality_issues.append("Tool returned empty string")
        elif isinstance(result, (list, dict)) and len(result) == 0:
            quality_issues.append("Tool returned empty collection")
        
        enhanced_result['enhanced_data'] = enhanced_data
        enhanced_result['quality_issues'] = quality_issues
        enhanced_result['recommendations'] = ["Verify tool configuration and data source"]
        enhanced_result['enhancement_applied'] = len(quality_issues) > 0
        
        return enhanced_result
    
    def _calculate_quality_score(self, enhanced_result: Dict) -> float:
        """Calculate overall quality score for enhanced result."""
        base_score = 1.0
        
        # Deduct points for quality issues
        issues_count = len(enhanced_result.get('quality_issues', []))
        quality_penalty = min(issues_count * 0.1, 0.5)  # Max 50% penalty
        
        # Add points for enhancements
        enhancement_bonus = 0.1 if enhanced_result.get('enhancement_applied') else 0
        
        final_score = max(0.0, min(1.0, base_score - quality_penalty + enhancement_bonus))
        return round(final_score, 2)
    
    def _identify_market_type(self, symbol: str) -> str:
        """Identify market type from symbol."""
        symbol = symbol.upper()
        if any(crypto in symbol for crypto in ['BTC', 'ETH', 'SOL', 'USDT']):
            return 'crypto'
        elif any(indian in symbol for indian in ['NSEI', 'NSEBANK', 'BSESN']):
            return 'indian_equity'
        else:
            return 'global_equity'
    
    def _get_trading_hours(self, symbol: str) -> str:
        """Get trading hours for symbol."""
        market_type = self._identify_market_type(symbol)
        
        if market_type == 'crypto':
            return '24/7'
        elif market_type == 'indian_equity':
            return '09:15-15:30 IST'
        else:
            return '09:30-16:00 EST'
    
    def _calculate_news_relevance(self, news_item: Dict, symbol: str) -> float:
        """Calculate news relevance score for given symbol."""
        try:
            title = news_item.get('title', '').lower()
            summary = news_item.get('summary', '').lower()
            content = f"{title} {summary}"
            
            # Symbol-specific keywords
            symbol_keywords = {
                'BTCUSDT': ['bitcoin', 'btc', 'crypto', 'cryptocurrency'],
                'ETHUSDT': ['ethereum', 'eth', 'crypto', 'defi'],
                'SOLUSDT': ['solana', 'sol', 'crypto', 'blockchain'],
                '^NSEI': ['nifty', 'nse', 'indian market', 'sensex'],
                '^NSEBANK': ['bank nifty', 'banking', 'banks', 'financial']
            }
            
            keywords = symbol_keywords.get(symbol.upper(), [])
            if not keywords:
                return 0.5  # Default relevance
            
            # Count keyword matches
            matches = sum(1 for keyword in keywords if keyword in content)
            relevance_score = min(1.0, matches / len(keywords) + 0.3)
            
            return round(relevance_score, 2)
            
        except Exception:
            return 0.5  # Default relevance on error
    
    def get_enhancement_stats(self) -> Dict[str, Any]:
        """Get tool enhancement statistics."""
        return self.enhancement_stats.copy()

# Global instance
tool_quality_enhancer = ToolQualityEnhancer()
