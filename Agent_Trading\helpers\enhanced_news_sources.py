"""
Enhanced News Sources for Indian Markets
Provides comprehensive news coverage with better extraction and validation
"""

import requests
import feedparser
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
import re
from bs4 import BeautifulSoup

logger = logging.getLogger(__name__)

class EnhancedNewsExtractor:
    """Enhanced news extraction with better validation and Indian market focus"""
    
    def __init__(self):
        self.indian_news_sources = {
            'economic_times': {
                'url': 'https://economictimes.indiatimes.com/markets/rss/markets',
                'priority': 'high',
                'focus': ['nifty', 'bank nifty', 'sensex', 'indian markets']
            },
            'moneycontrol': {
                'url': 'https://www.moneycontrol.com/rss/marketstocks.xml',
                'priority': 'high',
                'focus': ['stocks', 'derivatives', 'market analysis']
            },
            'business_standard': {
                'url': 'https://www.business-standard.com/rss/markets-106.rss',
                'priority': 'medium',
                'focus': ['market news', 'policy', 'corporate']
            },
            'livemint': {
                'url': 'https://www.livemint.com/rss/markets',
                'priority': 'medium',
                'focus': ['market trends', 'analysis', 'global markets']
            },
            'financial_express': {
                'url': 'https://www.financialexpress.com/market/rss/',
                'priority': 'medium',
                'focus': ['market updates', 'economy', 'policy']
            }
        }
        
        self.global_news_sources = {
            'reuters_markets': {
                'url': 'https://feeds.reuters.com/reuters/businessNews',
                'priority': 'high',
                'focus': ['global markets', 'economy', 'central banks']
            },
            'bloomberg_markets': {
                'url': 'https://feeds.bloomberg.com/markets/news.rss',
                'priority': 'high',
                'focus': ['markets', 'trading', 'analysis']
            },
            'cnbc_markets': {
                'url': 'https://www.cnbc.com/id/*********/device/rss/rss.html',
                'priority': 'medium',
                'focus': ['us markets', 'trading', 'stocks']
            }
        }
        
        # Keywords for relevance scoring
        self.indian_market_keywords = [
            'nifty', 'bank nifty', 'sensex', 'bse', 'nse', 'rbi', 'india',
            'rupee', 'fii', 'dii', 'sebi', 'derivatives', 'options'
        ]
        
        self.trading_keywords = [
            'trading', 'technical analysis', 'support', 'resistance', 'breakout',
            'trend', 'volume', 'momentum', 'volatility', 'signals'
        ]
    
    def get_comprehensive_news(self, symbol: str = None, max_articles: int = 20) -> Dict[str, Any]:
        """Get comprehensive news with enhanced extraction and validation"""
        try:
            all_news = []
            
            # Determine focus based on symbol
            is_indian_symbol = self._is_indian_symbol(symbol) if symbol else True
            
            # Get Indian market news (always include for comprehensive coverage)
            indian_news = self._fetch_indian_news(max_articles // 2)
            all_news.extend(indian_news)
            
            # Get global news for context
            global_news = self._fetch_global_news(max_articles // 2)
            all_news.extend(global_news)
            
            # Filter and rank by relevance
            if symbol:
                all_news = self._filter_by_symbol_relevance(all_news, symbol)
            
            # Sort by relevance and recency
            all_news = self._rank_news_by_relevance(all_news, is_indian_symbol)
            
            # Limit to max articles
            all_news = all_news[:max_articles]
            
            # Analyze sentiment and extract key themes
            sentiment_analysis = self._analyze_market_sentiment(all_news)
            key_themes = self._extract_key_themes(all_news)
            
            return {
                'news': all_news,
                'total_articles': len(all_news),
                'sentiment_analysis': sentiment_analysis,
                'key_themes': key_themes,
                'market_focus': 'indian' if is_indian_symbol else 'global',
                'extraction_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Comprehensive news extraction failed: {e}")
            return {
                'news': [],
                'error': str(e),
                'sentiment_analysis': {'overall': 'neutral', 'confidence': 0},
                'key_themes': []
            }
    
    def _is_indian_symbol(self, symbol: str) -> bool:
        """Check if symbol is related to Indian markets"""
        if not symbol:
            return False
        
        symbol_upper = symbol.upper()
        indian_indicators = ['^NSEI', '^NSEBANK', '^BSESN', 'BANKNIFTY', 'NIFTY', 'INR', 'USDINR']
        
        return any(indicator in symbol_upper for indicator in indian_indicators)
    
    def _fetch_indian_news(self, max_articles: int) -> List[Dict]:
        """Fetch news from Indian sources"""
        news_items = []
        
        for source_name, source_info in self.indian_news_sources.items():
            try:
                articles = self._fetch_rss_feed(source_info['url'], source_name)
                # Add source priority and focus info
                for article in articles:
                    article['source_priority'] = source_info['priority']
                    article['source_focus'] = source_info['focus']
                    article['market_region'] = 'indian'
                
                news_items.extend(articles[:max_articles // len(self.indian_news_sources)])
                
            except Exception as e:
                logger.warning(f"Failed to fetch from {source_name}: {e}")
        
        return news_items
    
    def _fetch_global_news(self, max_articles: int) -> List[Dict]:
        """Fetch news from global sources"""
        news_items = []
        
        for source_name, source_info in self.global_news_sources.items():
            try:
                articles = self._fetch_rss_feed(source_info['url'], source_name)
                # Add source priority and focus info
                for article in articles:
                    article['source_priority'] = source_info['priority']
                    article['source_focus'] = source_info['focus']
                    article['market_region'] = 'global'
                
                news_items.extend(articles[:max_articles // len(self.global_news_sources)])
                
            except Exception as e:
                logger.warning(f"Failed to fetch from {source_name}: {e}")
        
        return news_items
    
    def _fetch_rss_feed(self, url: str, source_name: str) -> List[Dict]:
        """Fetch and parse RSS feed"""
        try:
            # For now, return sample data structure
            # In production, implement actual RSS parsing with feedparser
            
            sample_articles = [
                {
                    'title': f'Market Update from {source_name.title()}',
                    'summary': f'Latest market developments and analysis from {source_name}',
                    'link': url,
                    'published': datetime.now().isoformat(),
                    'source': source_name,
                    'relevance_score': 0.8,
                    'sentiment': 'neutral'
                }
            ]
            
            return sample_articles
            
        except Exception as e:
            logger.error(f"RSS fetch failed for {url}: {e}")
            return []
    
    def _filter_by_symbol_relevance(self, news_items: List[Dict], symbol: str) -> List[Dict]:
        """Filter news by symbol relevance"""
        symbol_keywords = [symbol.lower(), symbol.replace('^', '').lower()]
        
        # Add specific keywords based on symbol
        if 'NIFTY' in symbol.upper():
            symbol_keywords.extend(['nifty', 'index', 'indian market'])
        elif 'BANK' in symbol.upper():
            symbol_keywords.extend(['bank', 'banking', 'financial'])
        
        filtered_news = []
        for article in news_items:
            relevance_score = self._calculate_relevance_score(article, symbol_keywords)
            if relevance_score > 0.3:  # Threshold for relevance
                article['relevance_score'] = relevance_score
                filtered_news.append(article)
        
        return filtered_news
    
    def _calculate_relevance_score(self, article: Dict, keywords: List[str]) -> float:
        """Calculate relevance score for an article"""
        text = f"{article.get('title', '')} {article.get('summary', '')}".lower()
        
        score = 0.0
        for keyword in keywords:
            if keyword in text:
                score += 0.2
        
        # Boost score for Indian market keywords if applicable
        for keyword in self.indian_market_keywords:
            if keyword in text:
                score += 0.1
        
        # Boost score for trading-related keywords
        for keyword in self.trading_keywords:
            if keyword in text:
                score += 0.1
        
        return min(score, 1.0)  # Cap at 1.0
    
    def _rank_news_by_relevance(self, news_items: List[Dict], prefer_indian: bool) -> List[Dict]:
        """Rank news by relevance and recency"""
        def sort_key(article):
            relevance = article.get('relevance_score', 0.5)
            priority_boost = 0.2 if article.get('source_priority') == 'high' else 0.0
            indian_boost = 0.1 if prefer_indian and article.get('market_region') == 'indian' else 0.0
            
            return relevance + priority_boost + indian_boost
        
        return sorted(news_items, key=sort_key, reverse=True)
    
    def _analyze_market_sentiment(self, news_items: List[Dict]) -> Dict[str, Any]:
        """Analyze overall market sentiment from news"""
        if not news_items:
            return {'overall': 'neutral', 'confidence': 0}
        
        positive_words = ['surge', 'rally', 'gain', 'bullish', 'positive', 'growth', 'rise']
        negative_words = ['fall', 'decline', 'bearish', 'negative', 'crash', 'drop', 'loss']
        
        positive_count = 0
        negative_count = 0
        
        for article in news_items:
            text = f"{article.get('title', '')} {article.get('summary', '')}".lower()
            
            for word in positive_words:
                positive_count += text.count(word)
            
            for word in negative_words:
                negative_count += text.count(word)
        
        total_sentiment_words = positive_count + negative_count
        
        if total_sentiment_words == 0:
            return {'overall': 'neutral', 'confidence': 0}
        
        positive_ratio = positive_count / total_sentiment_words
        
        if positive_ratio > 0.6:
            sentiment = 'bullish'
        elif positive_ratio < 0.4:
            sentiment = 'bearish'
        else:
            sentiment = 'neutral'
        
        confidence = abs(positive_ratio - 0.5) * 2  # 0 to 1 scale
        
        return {
            'overall': sentiment,
            'confidence': round(confidence, 2),
            'positive_mentions': positive_count,
            'negative_mentions': negative_count
        }
    
    def _extract_key_themes(self, news_items: List[Dict]) -> List[str]:
        """Extract key themes from news articles"""
        themes = []
        
        # Common market themes to look for
        theme_keywords = {
            'monetary_policy': ['rbi', 'fed', 'interest rate', 'policy', 'inflation'],
            'market_volatility': ['volatility', 'vix', 'uncertainty', 'fluctuation'],
            'sector_rotation': ['sector', 'rotation', 'banking', 'it', 'pharma'],
            'global_markets': ['global', 'us market', 'china', 'europe'],
            'institutional_flows': ['fii', 'dii', 'institutional', 'foreign investment']
        }
        
        for theme_name, keywords in theme_keywords.items():
            theme_count = 0
            for article in news_items:
                text = f"{article.get('title', '')} {article.get('summary', '')}".lower()
                for keyword in keywords:
                    if keyword in text:
                        theme_count += 1
                        break
            
            if theme_count >= 2:  # Theme appears in at least 2 articles
                themes.append(theme_name.replace('_', ' ').title())
        
        return themes

# Global instance
enhanced_news_extractor = EnhancedNewsExtractor()
