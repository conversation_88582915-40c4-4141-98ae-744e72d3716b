Requirement already satisfied: google-generativeai in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (0.8.5)
Requirement already satisfied: google-ai-generativelanguage==0.6.15 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from google-generativeai) (0.6.15)
Requirement already satisfied: google-api-core in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from google-generativeai) (2.25.1)
Requirement already satisfied: google-api-python-client in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from google-generativeai) (2.176.0)
Requirement already satisfied: google-auth>=2.15.0 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from google-generativeai) (2.40.3)
Requirement already satisfied: protobuf in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from google-generativeai) (5.29.5)
Requirement already satisfied: pydantic in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from google-generativeai) (2.11.7)
Requirement already satisfied: tqdm in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from google-generativeai) (4.67.1)
Requirement already satisfied: typing-extensions in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from google-generativeai) (4.14.1)
Requirement already satisfied: proto-plus<2.0.0dev,>=1.22.3 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from google-ai-generativelanguage==0.6.15->google-generativeai) (1.26.1)
Requirement already satisfied: googleapis-common-protos<2.0.0,>=1.56.2 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from google-api-core->google-generativeai) (1.70.0)
Requirement already satisfied: requests<3.0.0,>=2.18.0 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from google-api-core->google-generativeai) (2.32.4)
Requirement already satisfied: grpcio<2.0.0,>=1.33.2 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0dev,>=1.34.1->google-ai-generativelanguage==0.6.15->google-generativeai) (1.73.1)
Requirement already satisfied: grpcio-status<2.0.0,>=1.33.2 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0dev,>=1.34.1->google-ai-generativelanguage==0.6.15->google-generativeai) (1.71.2)
Requirement already satisfied: cachetools<6.0,>=2.0.0 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from google-auth>=2.15.0->google-generativeai) (5.5.2)
Requirement already satisfied: pyasn1-modules>=0.2.1 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from google-auth>=2.15.0->google-generativeai) (0.4.2)
Requirement already satisfied: rsa<5,>=3.1.4 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from google-auth>=2.15.0->google-generativeai) (4.9.1)
Requirement already satisfied: charset_normalizer<4,>=2 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from requests<3.0.0,>=2.18.0->google-api-core->google-generativeai) (3.4.2)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from requests<3.0.0,>=2.18.0->google-api-core->google-generativeai) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from requests<3.0.0,>=2.18.0->google-api-core->google-generativeai) (2.4.0)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from requests<3.0.0,>=2.18.0->google-api-core->google-generativeai) (2025.7.14)
Requirement already satisfied: pyasn1>=0.1.3 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from rsa<5,>=3.1.4->google-auth>=2.15.0->google-generativeai) (0.6.1)
Requirement already satisfied: httplib2<1.0.0,>=0.19.0 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from google-api-python-client->google-generativeai) (0.22.0)
Requirement already satisfied: google-auth-httplib2<1.0.0,>=0.2.0 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from google-api-python-client->google-generativeai) (0.2.0)
Requirement already satisfied: uritemplate<5,>=3.0.1 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from google-api-python-client->google-generativeai) (4.2.0)
Requirement already satisfied: pyparsing!=3.0.0,!=3.0.1,!=3.0.2,!=3.0.3,<4,>=2.4.2 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from httplib2<1.0.0,>=0.19.0->google-api-python-client->google-generativeai) (3.2.3)
Requirement already satisfied: annotated-types>=0.6.0 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from pydantic->google-generativeai) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from pydantic->google-generativeai) (2.33.2)
Requirement already satisfied: typing-inspection>=0.4.0 in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from pydantic->google-generativeai) (0.4.1)
Requirement already satisfied: colorama in c:\users\<USER>\anaconda3\envs\venv\lib\site-packages (from tqdm->google-generativeai) (0.4.6)
