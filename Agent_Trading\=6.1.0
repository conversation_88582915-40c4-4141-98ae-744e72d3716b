Defaulting to user installation because normal site-packages is not writeable
Collecting duckduckgo-search
  Downloading duckduckgo_search-8.1.1-py3-none-any.whl.metadata (16 kB)
Requirement already satisfied: click>=8.1.8 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from duckduckgo-search) (8.1.8)
Collecting primp>=0.15.0 (from duckduckgo-search)
  Downloading primp-0.15.0-cp38-abi3-win_amd64.whl.metadata (13 kB)
Collecting lxml>=5.3.0 (from duckduckgo-search)
  Downloading lxml-6.0.0-cp313-cp313-win_amd64.whl.metadata (6.8 kB)
Requirement already satisfied: colorama in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from click>=8.1.8->duckduckgo-search) (0.4.6)
Downloading duckduckgo_search-8.1.1-py3-none-any.whl (18 kB)
Downloading lxml-6.0.0-cp313-cp313-win_amd64.whl (4.0 MB)
   ---------------------------------------- 4.0/4.0 MB 999.3 kB/s  0:00:03
Downloading primp-0.15.0-cp38-abi3-win_amd64.whl (3.1 MB)
   ---------------------------------------- 3.1/3.1 MB 1.1 MB/s  0:00:03
Installing collected packages: primp, lxml, duckduckgo-search

Successfully installed duckduckgo-search-8.1.1 lxml-6.0.0 primp-0.15.0
