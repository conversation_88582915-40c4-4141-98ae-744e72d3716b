"""Enhanced tool management system for AI trading analysis."""

from __future__ import annotations

import json
import logging
from typing import Dict, Any, Callable, List, Optional
from dataclasses import dataclass
import google.generativeai as genai
from .data_tools import (
    fetch_market_data, fetch_multiple_timeframes, get_market_context,
    fetch_crypto_data, fetch_multiple_crypto_timeframes, get_crypto_market_context,
    fetch_comprehensive_market_data, get_fii_dii_flows
)
from .data_router import (
    get_smart_market_data, get_symbol_routing_info,
    get_comprehensive_market_news, get_market_context_summary
)
from .dhan_api import get_dhan_market_data, is_dhan_available, get_supported_indian_symbols
from .advanced_trading_tools import get_risk_management_analysis, get_trading_signals
from .indian_market_tools import indian_market_analyzer

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class ToolResult:
    """Standardized tool result format."""
    success: bool
    data: Any
    error: Optional[str] = None
    execution_time: Optional[float] = None
    tool_name: Optional[str] = None


class ToolRegistry:
    """Registry for managing AI tools with validation and monitoring."""
    
    def __init__(self):
        self.tools: Dict[str, Callable] = {}
        self.tool_definitions: List[genai.protos.FunctionDeclaration] = []
        self.tool_stats: Dict[str, Dict[str, Any]] = {}
        self.indian_analyzer = indian_market_analyzer
        self._register_default_tools()
    
    def _register_default_tools(self):
        """Register the default trading analysis tools."""
        
        # Market data tool
        self.register_tool(
            name="fetch_market_data",
            function=fetch_market_data,
            description="Fetch market data for a specific symbol, period, and interval.",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {"type": "string", "description": "Ticker symbol (e.g., 'AAPL', 'BTC-USD')"},
                    "period": {"type": "string", "description": "Time period ('1d', '1mo', '1y', etc.)"},
                    "interval": {"type": "string", "description": "Data interval ('1m', '1h', '1d', etc.)"},
                    "reason": {"type": "string", "description": "Why this data is needed for the analysis"}
                },
                "required": ["symbol", "period", "interval", "reason"]
            }
        )
        
        # Multiple timeframes tool
        self.register_tool(
            name="fetch_multiple_timeframes",
            function=fetch_multiple_timeframes,
            description="Fetch market data for multiple timeframes for comprehensive analysis.",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {"type": "string", "description": "Ticker symbol"},
                    "timeframes": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "period": {"type": "string"},
                                "interval": {"type": "string"}
                            },
                            "required": ["period", "interval"]
                        },
                        "description": "List of timeframes"
                    },
                    "reason": {"type": "string", "description": "Why multi-timeframe data is needed"}
                },
                "required": ["symbol", "reason"]
            }
        )
        
        # Market context tool
        self.register_tool(
            name="get_market_context",
            function=get_market_context,
            description="Get broader market context by fetching data for major indices.",
            parameters={
                "type": "object",
                "properties": {
                    "symbols": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "List of symbols to fetch (defaults to major indices)"
                    },
                    "reason": {"type": "string", "description": "Why market context is needed"}
                },
                "required": ["reason"]
            }
        )

        # Crypto data tool
        self.register_tool(
            name="fetch_crypto_data",
            function=fetch_crypto_data,
            description="Fetch crypto market data from Delta Exchange for BTC, ETH, SOL.",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {"type": "string", "description": "Crypto symbol (BTCUSD, ETHUSD, SOLUSD)"},
                    "period": {"type": "string", "description": "Time period (1d, 7d, 30d, 90d)"},
                    "interval": {"type": "string", "description": "Data interval (1m, 5m, 15m, 1h, 4h, 1d)"},
                    "reason": {"type": "string", "description": "Why this crypto data is needed"}
                },
                "required": ["symbol", "reason"]
            }
        )

        # Multiple crypto timeframes tool
        self.register_tool(
            name="fetch_multiple_crypto_timeframes",
            function=fetch_multiple_crypto_timeframes,
            description="Fetch crypto data for multiple timeframes for comprehensive analysis.",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {"type": "string", "description": "Crypto symbol (BTCUSD, ETHUSD, SOLUSD)"},
                    "timeframes": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "period": {"type": "string"},
                                "interval": {"type": "string"}
                            },
                            "required": ["period", "interval"]
                        },
                        "description": "List of timeframes for crypto analysis"
                    },
                    "reason": {"type": "string", "description": "Why multi-timeframe crypto data is needed"}
                },
                "required": ["symbol", "reason"]
            }
        )

        # Crypto market context tool
        self.register_tool(
            name="get_crypto_market_context",
            function=get_crypto_market_context,
            description="Get broader crypto market context for BTC, ETH, SOL.",
            parameters={
                "type": "object",
                "properties": {
                    "symbols": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "List of crypto symbols (defaults to BTCUSD, ETHUSD, SOLUSD)"
                    },
                    "reason": {"type": "string", "description": "Why crypto market context is needed"}
                },
                "required": ["reason"]
            }
        )

        # Smart data router tool
        self.register_tool(
            name="get_smart_market_data",
            function=get_smart_market_data,
            description="Automatically route data requests to the best source (Delta Exchange for crypto, yfinance for stocks/indices).",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {"type": "string", "description": "Trading symbol (BTCUSDT, ^NSEI, AAPL, etc.)"},
                    "period": {"type": "string", "description": "Time period ('1d', '1mo', '1y', etc.)"},
                    "interval": {"type": "string", "description": "Data interval ('1m', '1h', '1d', etc.)"},
                    "comprehensive": {"type": "boolean", "description": "Include news, recommendations, company info"},
                    "reason": {"type": "string", "description": "Why this data is needed for analysis"}
                },
                "required": ["symbol", "period", "interval", "reason"]
            }
        )

        # Symbol routing info tool
        self.register_tool(
            name="get_symbol_routing_info",
            function=get_symbol_routing_info,
            description="Get information about which data source will be used for a symbol.",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {"type": "string", "description": "Trading symbol to check routing for"}
                },
                "required": ["symbol"]
            }
        )

        # Enhanced Indian market news tool
        self.register_tool(
            name="get_comprehensive_market_news",
            function=self._get_enhanced_market_news,
            description="Get comprehensive market news with enhanced Indian market coverage including Economic Times, Moneycontrol, and sector-specific analysis. Automatically detects Indian symbols for specialized coverage.",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {"type": "string", "description": "Trading symbol (^NSEI, ^NSEBANK, BTCUSDT, etc.) - optional for general market news"},
                    "max_news": {"type": "integer", "description": "Maximum number of news items to return (default: 20)"}
                },
                "required": []
            }
        )

        # Market context summary tool
        self.register_tool(
            name="get_market_context_summary",
            function=get_market_context_summary,
            description="Get comprehensive market context including price data, news summary, and key market factors. Can be called without symbol for general market analysis or with symbol for specific asset analysis.",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {"type": "string", "description": "Trading symbol to get context for (optional - if not provided, returns general market context)"}
                },
                "required": []
            }
        )

        # FII/DII flows tool (crucial for Indian markets)
        self.register_tool(
            name="get_fii_dii_flows",
            function=get_fii_dii_flows,
            description="Get FII/DII (Foreign/Domestic Institutional Investor) flow data and analysis. Critical for Indian market analysis as institutional flows drive market direction.",
            parameters={
                "type": "object",
                "properties": {
                    "period": {"type": "string", "description": "Time period for flow analysis (1d, 5d, 1mo, 3mo)"},
                    "reason": {"type": "string", "description": "Why FII/DII flow data is needed for analysis"}
                },
                "required": ["reason"]
            }
        )

        # Dhan API direct access tool
        self.register_tool(
            name="get_dhan_market_data",
            function=get_dhan_market_data,
            description="Get high-quality Indian market data directly from Dhan API. Preferred over yfinance for Indian stocks and indices when available.",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {"type": "string", "description": "Indian market symbol (^NSEI, ^NSEBANK, RELIANCE.NS, etc.)"},
                    "period": {"type": "string", "description": "Time period (1d, 5d, 1mo, 3mo, 6mo, 1y)"},
                    "interval": {"type": "string", "description": "Data interval (1m, 5m, 15m, 1h, 1d)"},
                    "reason": {"type": "string", "description": "Why Dhan API data is needed"}
                },
                "required": ["symbol", "reason"]
            }
        )

        # Dhan API status check tool
        self.register_tool(
            name="check_dhan_api_status",
            function=is_dhan_available,
            description="Check if Dhan API is available and properly configured for Indian market data.",
            parameters={
                "type": "object",
                "properties": {},
                "required": []
            }
        )

        # Technical analysis tool
        self.register_tool(
            name="get_technical_analysis",
            function=self._get_technical_analysis,
            description="Get comprehensive technical analysis including RSI, MACD, moving averages, support/resistance levels, and trading signals for any symbol",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {"type": "string", "description": "Trading symbol to analyze"},
                    "timeframe": {"type": "string", "description": "Timeframe for analysis (1d, 1h, 5m)", "default": "1d"},
                    "period": {"type": "string", "description": "Data period (1mo, 3mo, 6mo, 1y)", "default": "3mo"}
                },
                "required": ["symbol"]
            }
        )

        # Advanced risk management tool
        self.register_tool(
            name="get_risk_management_analysis",
            function=self._get_risk_management_analysis,
            description="Calculate position sizing, risk/reward ratios, and risk management recommendations for a trade setup",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {"type": "string", "description": "Trading symbol"},
                    "entry_price": {"type": "number", "description": "Planned entry price"},
                    "stop_loss": {"type": "number", "description": "Stop loss price"},
                    "take_profit": {"type": "number", "description": "Take profit target price"},
                    "account_balance": {"type": "number", "description": "Account balance for position sizing", "default": 100000}
                },
                "required": ["symbol", "entry_price", "stop_loss", "take_profit"]
            }
        )

        # Advanced trading signals tool
        self.register_tool(
            name="get_advanced_trading_signals",
            function=self._get_advanced_trading_signals,
            description="Generate specific entry/exit signals with price levels based on technical and market analysis",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {"type": "string", "description": "Trading symbol to analyze"},
                    "timeframe": {"type": "string", "description": "Timeframe for signals (1d, 1h, 5m)", "default": "1d"}
                },
                "required": ["symbol"]
            }
        )

        # Indian market options analysis tool
        self.register_tool(
            name="get_options_chain_analysis",
            function=self._get_options_analysis,
            description="Get options chain analysis for Indian indices including max pain, put-call ratio, and key levels",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {"type": "string", "description": "Indian index symbol (^NSEI, ^NSEBANK)"}
                },
                "required": ["symbol"]
            }
        )

        # Smart symbol detection tool
        self.register_tool(
            name="detect_trading_symbol",
            function=self._detect_symbol,
            description="Intelligently detect the correct trading symbol from chart text or user input, especially for Indian futures",
            parameters={
                "type": "object",
                "properties": {
                    "text_input": {"type": "string", "description": "Text from chart or user input to analyze for symbol detection"}
                },
                "required": ["text_input"]
            }
        )

    def register_tool(self, name: str, function: Callable, description: str, parameters: Dict[str, Any]):
        """Register a new tool with the registry."""
        self.tools[name] = function

        # Create Gemini function declaration with proper schema
        func_declaration = genai.protos.FunctionDeclaration(
            name=name,
            description=description,
            parameters=genai.protos.Schema(
                type=genai.protos.Type.OBJECT,
                properties={
                    prop_name: genai.protos.Schema(
                        type=self._get_proto_type(prop_def.get("type", "string")),
                        description=prop_def.get("description", ""),
                        items=self._convert_items_schema(prop_def.get("items")) if prop_def.get("items") else None
                    )
                    for prop_name, prop_def in parameters.get("properties", {}).items()
                },
                required=parameters.get("required", [])
            )
        )
        self.tool_definitions.append(func_declaration)
        
        # Initialize stats
        self.tool_stats[name] = {
            "calls": 0,
            "successes": 0,
            "failures": 0,
            "avg_execution_time": 0.0
        }
        
        logger.info(f"Registered tool: {name}")

    def _get_proto_type(self, type_str: str):
        """Convert string type to proto Type enum."""
        type_mapping = {
            "string": genai.protos.Type.STRING,
            "number": genai.protos.Type.NUMBER,
            "integer": genai.protos.Type.INTEGER,
            "boolean": genai.protos.Type.BOOLEAN,
            "array": genai.protos.Type.ARRAY,
            "object": genai.protos.Type.OBJECT
        }
        return type_mapping.get(type_str, genai.protos.Type.STRING)

    def _convert_items_schema(self, items_def):
        """Convert items definition to proto Schema."""
        if not items_def:
            return None

        if items_def.get("type") == "object":
            return genai.protos.Schema(
                type=genai.protos.Type.OBJECT,
                properties={
                    prop_name: genai.protos.Schema(
                        type=self._get_proto_type(prop_def.get("type", "string")),
                        description=prop_def.get("description", "")
                    )
                    for prop_name, prop_def in items_def.get("properties", {}).items()
                },
                required=items_def.get("required", [])
            )
        else:
            return genai.protos.Schema(
                type=self._get_proto_type(items_def.get("type", "string")),
                description=items_def.get("description", "")
            )

    def execute_tool(self, name: str, **kwargs) -> ToolResult:
        """Execute a tool with monitoring and error handling."""
        import time
        
        if name not in self.tools:
            error_msg = f"Tool '{name}' not found in registry"
            logger.error(error_msg)
            return ToolResult(success=False, data=None, error=error_msg, tool_name=name)
        
        start_time = time.time()
        self.tool_stats[name]["calls"] += 1
        
        try:
            logger.info(f"Executing tool: {name} with args: {kwargs}")
            result = self.tools[name](**kwargs)
            execution_time = time.time() - start_time
            
            # Update stats
            self.tool_stats[name]["successes"] += 1
            self._update_avg_execution_time(name, execution_time)
            
            logger.info(f"Tool {name} executed successfully in {execution_time:.2f}s")
            return ToolResult(
                success=True, 
                data=result, 
                execution_time=execution_time,
                tool_name=name
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.tool_stats[name]["failures"] += 1
            error_msg = f"Tool {name} failed: {str(e)}"
            logger.error(error_msg)
            
            return ToolResult(
                success=False, 
                data=None, 
                error=error_msg,
                execution_time=execution_time,
                tool_name=name
            )
    
    def _update_avg_execution_time(self, name: str, execution_time: float):
        """Update average execution time for a tool."""
        stats = self.tool_stats[name]
        total_calls = stats["successes"]
        current_avg = stats["avg_execution_time"]
        
        # Calculate new average
        new_avg = ((current_avg * (total_calls - 1)) + execution_time) / total_calls
        stats["avg_execution_time"] = new_avg

    def _get_technical_analysis(self, symbol: str, timeframe: str = "1d", period: str = "3mo") -> Dict[str, Any]:
        """Get comprehensive technical analysis for a symbol."""
        try:
            from .data_router import data_router

            # Get market data with technical indicators
            market_data = data_router.get_market_data(symbol, period, timeframe, comprehensive=True,
                                                     reason="Technical analysis")

            if not market_data.get("data"):
                return {"error": "No market data available for technical analysis", "symbol": symbol}

            data_points = market_data["data"]

            # Calculate additional technical indicators
            closes = [point["close"] for point in data_points if point.get("close")]
            volumes = [point["volume"] for point in data_points if point.get("volume")]

            if len(closes) < 20:
                return {"error": "Insufficient data for technical analysis", "symbol": symbol}

            # Support and resistance levels
            recent_highs = [point["high"] for point in data_points[-20:] if point.get("high")]
            recent_lows = [point["low"] for point in data_points[-20:] if point.get("low")]

            resistance = max(recent_highs) if recent_highs else None
            support = min(recent_lows) if recent_lows else None

            # Price momentum
            current_price = closes[-1]
            price_20_ago = closes[-20] if len(closes) >= 20 else closes[0]
            momentum_20d = ((current_price - price_20_ago) / price_20_ago * 100) if price_20_ago else 0

            # Volume analysis
            avg_volume = sum(volumes[-10:]) / len(volumes[-10:]) if volumes else 0
            current_volume = volumes[-1] if volumes else 0
            volume_ratio = (current_volume / avg_volume) if avg_volume > 0 else 1

            # Get latest technical indicators from data
            latest_data = data_points[-1]

            analysis = {
                "symbol": symbol,
                "timeframe": timeframe,
                "period": period,
                "current_price": current_price,
                "technical_indicators": {
                    "rsi": latest_data.get("rsi"),
                    "sma_20": latest_data.get("sma_20"),
                    "sma_50": latest_data.get("sma_50"),
                    "momentum_20d": round(momentum_20d, 2)
                },
                "support_resistance": {
                    "support": round(support, 2) if support else None,
                    "resistance": round(resistance, 2) if resistance else None,
                    "distance_to_support": round(((current_price - support) / support * 100), 2) if support else None,
                    "distance_to_resistance": round(((resistance - current_price) / current_price * 100), 2) if resistance else None
                },
                "volume_analysis": {
                    "current_volume": current_volume,
                    "avg_volume_10d": round(avg_volume, 0),
                    "volume_ratio": round(volume_ratio, 2),
                    "volume_signal": "High" if volume_ratio > 1.5 else "Normal" if volume_ratio > 0.8 else "Low"
                },
                "trading_signals": self._generate_trading_signals(latest_data, support, resistance, momentum_20d),
                "success": True
            }

            return analysis

        except Exception as e:
            return {"error": f"Technical analysis failed: {str(e)}", "symbol": symbol, "success": False}

    def _generate_trading_signals(self, latest_data: Dict, support: float, resistance: float, momentum: float) -> Dict[str, Any]:
        """Generate trading signals based on technical analysis."""
        signals = []
        overall_signal = "NEUTRAL"
        confidence = 0

        rsi = latest_data.get("rsi")
        sma_20 = latest_data.get("sma_20")
        sma_50 = latest_data.get("sma_50")
        current_price = latest_data.get("close")

        # RSI signals
        if rsi:
            if rsi < 30:
                signals.append("RSI oversold - potential buy signal")
                confidence += 20
                overall_signal = "BUY"
            elif rsi > 70:
                signals.append("RSI overbought - potential sell signal")
                confidence += 20
                overall_signal = "SELL"

        # Moving average signals
        if sma_20 and sma_50 and current_price:
            if current_price > sma_20 > sma_50:
                signals.append("Price above both moving averages - bullish trend")
                confidence += 15
                if overall_signal != "SELL":
                    overall_signal = "BUY"
            elif current_price < sma_20 < sma_50:
                signals.append("Price below both moving averages - bearish trend")
                confidence += 15
                overall_signal = "SELL"

        # Support/resistance signals
        if support and resistance and current_price:
            if current_price <= support * 1.02:  # Within 2% of support
                signals.append("Price near support level - potential bounce")
                confidence += 10
            elif current_price >= resistance * 0.98:  # Within 2% of resistance
                signals.append("Price near resistance level - potential reversal")
                confidence += 10

        # Momentum signals
        if momentum > 10:
            signals.append("Strong positive momentum")
            confidence += 10
        elif momentum < -10:
            signals.append("Strong negative momentum")
            confidence += 10

        return {
            "overall_signal": overall_signal,
            "confidence": min(confidence, 100),
            "signals": signals,
            "recommendation": f"{overall_signal} with {min(confidence, 100)}% confidence"
        }

    def _get_risk_management_analysis(self, symbol: str, entry_price: float,
                                    stop_loss: float, take_profit: float,
                                    account_balance: float = 100000) -> Dict[str, Any]:
        """Get comprehensive risk management analysis."""
        try:
            return get_risk_management_analysis(symbol, entry_price, stop_loss, take_profit, account_balance)
        except Exception as e:
            logger.error(f"Error in risk management analysis: {e}")
            return {"error": f"Risk management analysis failed: {str(e)}"}

    def _get_enhanced_market_news(self, symbol: str = None, max_news: int = 20) -> Dict[str, Any]:
        """Get enhanced market news with Indian market specialization."""
        try:
            # Check if it's an Indian market symbol
            indian_symbols = ['^NSEI', '^NSEBANK', '^BSESN', 'BANKNIFTY', 'NIFTY']

            if symbol and any(indian_sym in symbol.upper() for indian_sym in indian_symbols):
                # Use specialized Indian market news
                return self.indian_analyzer.get_enhanced_indian_news(symbol)
            else:
                # Use standard news function
                from .data_router import get_comprehensive_market_news
                return get_comprehensive_market_news(symbol, max_news)
        except Exception as e:
            logger.error(f"Error getting enhanced market news: {e}")
            return {"error": f"News retrieval failed: {str(e)}", "news": []}

    def _get_advanced_trading_signals(self, symbol: str, timeframe: str = "1d") -> Dict[str, Any]:
        """Get advanced trading signals with specific entry/exit levels."""
        try:
            from .data_router import data_router

            # Get market data
            market_data = data_router.get_market_data(symbol, "3mo", timeframe, comprehensive=True, reason="Advanced trading signals")

            # Get technical analysis
            technical_data = self._get_technical_analysis(symbol, timeframe, "3mo")

            # Generate signals
            signals = get_trading_signals(market_data, technical_data)

            return {
                "symbol": symbol,
                "timeframe": timeframe,
                "signals": signals,
                "market_data_summary": {
                    "current_price": market_data.get("current_price"),
                    "volume": market_data.get("summary", {}).get("avg_volume"),
                    "data_source": market_data.get("data_source")
                }
            }
        except Exception as e:
            logger.error(f"Error generating advanced trading signals: {e}")
            return {"error": f"Advanced trading signals failed: {str(e)}"}

    def _get_options_analysis(self, symbol: str) -> Dict[str, Any]:
        """Get options chain analysis for Indian indices."""
        try:
            return self.indian_analyzer.get_options_chain_analysis(symbol)
        except Exception as e:
            logger.error(f"Error in options analysis: {e}")
            return {"error": f"Options analysis failed: {str(e)}"}

    def _detect_symbol(self, text_input: str) -> Dict[str, Any]:
        """Detect trading symbol from text input."""
        try:
            detected_symbol = self.indian_analyzer.detect_symbol_from_text(text_input)
            futures_symbol = self.indian_analyzer.get_futures_symbol(detected_symbol)

            return {
                "detected_symbol": detected_symbol,
                "futures_symbol": futures_symbol,
                "symbol_type": "indian_index" if detected_symbol.startswith('^NSE') else "other",
                "confidence": "high" if any(keyword in text_input.lower() for keyword in ['nifty', 'bank', 'sensex']) else "medium"
            }
        except Exception as e:
            logger.error(f"Error in symbol detection: {e}")
            return {"error": f"Symbol detection failed: {str(e)}"}

    def get_tool_definitions(self) -> List[genai.protos.FunctionDeclaration]:
        """Get all tool definitions for Gemini."""
        return self.tool_definitions

    def get_tool_names(self) -> List[str]:
        """Get list of all registered tool names."""
        return list(self.tools.keys())
    
    def get_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get tool usage statistics."""
        return self.tool_stats
    
    def reset_stats(self):
        """Reset all tool statistics."""
        for name in self.tool_stats:
            self.tool_stats[name] = {
                "calls": 0,
                "successes": 0,
                "failures": 0,
                "avg_execution_time": 0.0
            }


# Global tool registry instance
tool_registry = ToolRegistry()


def get_tool_registry() -> ToolRegistry:
    """Get the global tool registry instance."""
    return tool_registry
