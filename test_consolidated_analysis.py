#!/usr/bin/env python3
"""
Test script for the consolidated analysis system.
This script verifies that the unified pipeline works correctly.
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from Agent_Trading.helpers.utils import (
    generate_analysis_cloud_optimized,
    extract_trading_levels_from_analysis,
    create_enhanced_chart_annotation
)
from Agent_Trading.helpers.chart_annotator import chart_annotator
from PIL import Image
import json

def test_level_extraction():
    """Test the enhanced level extraction function."""
    print("🧪 Testing level extraction...")
    
    # Mock analysis result with different formats
    test_analysis_1 = {
        "trade_ideas": [
            {
                "entry_price": 25000.50,
                "stop_loss": 24800.00,
                "take_profit": 25500.00,
                "direction": "Long"
            }
        ],
        "detailed_report": "Entry at 25000, stop loss 24800, target 25500"
    }
    
    test_analysis_2 = {
        "trade_setup": {
            "entry": 45000.75,
            "stop_loss": 44500.00,
            "target": 46000.00
        },
        "analysis_notes": "Buy at 45000 with SL 44500 and TP 46000"
    }
    
    # Test extraction
    levels_1 = extract_trading_levels_from_analysis(test_analysis_1)
    levels_2 = extract_trading_levels_from_analysis(test_analysis_2)
    
    print(f"✅ Test 1 levels: {levels_1}")
    print(f"✅ Test 2 levels: {levels_2}")
    
    # Test annotation data creation
    annotation_1 = create_enhanced_chart_annotation(test_analysis_1)
    annotation_2 = create_enhanced_chart_annotation(test_analysis_2)
    
    print(f"✅ Annotation 1: {annotation_1}")
    print(f"✅ Annotation 2: {annotation_2}")

def test_imports():
    """Test that all required imports work."""
    print("🧪 Testing imports...")
    
    try:
        from Agent_Trading.helpers.tool_manager import get_tool_registry
        registry = get_tool_registry()
        tool_names = registry.get_tool_names()
        print(f"✅ Tool registry loaded with {len(tool_names)} tools")
        
        from Agent_Trading.helpers.prompts import positional_prompt, scalp_prompt
        print("✅ Prompts loaded successfully")
        
        from Agent_Trading.helpers.memory_system import get_memory_system
        memory = get_memory_system()
        print("✅ Memory system loaded successfully")
        
        print("✅ All imports successful!")
        
    except Exception as e:
        print(f"❌ Import error: {e}")

def main():
    """Run all tests."""
    print("🚀 Testing Consolidated Analysis System\n")
    
    test_imports()
    print()
    
    test_level_extraction()
    print()
    
    print("✅ All tests completed!")

if __name__ == "__main__":
    main()
