"""Data fetching tools for LLM to use when needed."""
from __future__ import annotations

import pandas as pd
import yfinance as yf
import warnings
from contextlib import redirect_stderr
import io
from typing import Optional, Dict, Any
import json
from PIL import Image
from .delta_exchange import get_crypto_market_data, get_multiple_crypto_data, MAIN_CRYPTO_SYMBOLS


def fetch_market_data(
    symbol: str,
    period: str,
    interval: str,
    reason: str = ""
) -> Dict[str, Any]:
    """
    Fetch market data for a specific symbol.
    
    This tool should be used by the LLM when additional market data would enhance
    the trading analysis. The LLM should specify why this data is needed.
    
    Args:
        symbol: Ticker symbol (e.g., 'AAPL', 'BTC-USD', '^NSEI')
        period: Time period ('1d', '5d', '1mo', '3mo', '6mo', '1y', '2y', '5y', '10y', 'ytd', 'max').
            This parameter is required and must be specified by the caller.
        interval: Data interval ('1m', '2m', '5m', '15m', '30m', '60m', '90m', '1h', '4h', '1d', '5d', '1wk', '1mo', '3mo').
            This parameter is required and must be specified by the caller.
        reason: Why this data is needed for the analysis
        
    Returns:
        Dict containing the fetched data and metadata
    """
    try:
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            with redirect_stderr(io.StringIO()):
                ticker = yf.Ticker(symbol)
                data = ticker.history(period=period, interval=interval)
                
                if data.empty:
                    return {
                        "success": False,
                        "symbol": symbol,
                        "error": "No data found for the specified symbol and timeframe",
                        "reason": reason
                    }
                
                # Get recent data summary
                recent_data = data.tail(10)  # Last 10 data points
                current_price = data['Close'].iloc[-1]
                price_change = ((current_price - data['Close'].iloc[-2]) / data['Close'].iloc[-2] * 100) if len(data) > 1 else 0
                
                # Calculate some basic metrics
                high_52w = data['High'].max() if len(data) > 50 else data['High'].max()
                low_52w = data['Low'].min() if len(data) > 50 else data['Low'].min()
                avg_volume = data['Volume'].mean()
                
                return {
                    "success": True,
                    "symbol": symbol,
                    "period": period,
                    "interval": interval,
                    "reason": reason,
                    "current_price": round(current_price, 2),
                    "price_change_pct": round(price_change, 2),
                    "high_52w": round(high_52w, 2),
                    "low_52w": round(low_52w, 2),
                    "avg_volume": int(avg_volume),
                    "data_points": len(data),
                    "recent_data_csv": recent_data.to_csv(),
                    "timestamp_range": f"{data.index[0]} to {data.index[-1]}"
                }
                
    except Exception as e:
        return {
            "success": False,
            "symbol": symbol,
            "error": str(e),
            "reason": reason
        }


def fetch_comprehensive_market_data(symbol: str, period: str = "1mo", interval: str = "1d", reason: str = "") -> Dict[str, Any]:
    """
    Fetch comprehensive market data including news, recommendations, and financial data.
    Enhanced version that provides more context for analysis.
    """
    try:
        ticker = yf.Ticker(symbol)

        # Basic price data
        hist = ticker.history(period=period, interval=interval)
        if hist.empty:
            return {"error": f"No data found for symbol {symbol}"}

        # Comprehensive data collection
        data = {
            "symbol": symbol,
            "reason": reason,
            "price_data": {
                "current_price": float(hist['Close'].iloc[-1]),
                "previous_close": float(hist['Close'].iloc[-2]) if len(hist) > 1 else None,
                "high_52w": float(hist['High'].max()),
                "low_52w": float(hist['Low'].min()),
                "volume": int(hist['Volume'].iloc[-1]),
                "avg_volume": int(hist['Volume'].mean())
            },
            "ohlcv": hist.tail(50).to_dict('records'),  # Last 50 periods
        }

        # Try to get additional data (may not be available for all symbols)
        try:
            # News data - suppress yfinance warnings
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                news = ticker.news
                if news:
                    data["news"] = [
                        {
                            "title": item.get("title", ""),
                            "publisher": item.get("publisher", ""),
                            "link": item.get("link", ""),
                            "providerPublishTime": item.get("providerPublishTime", "")
                        }
                        for item in news[:5]  # Latest 5 news items
                    ]
        except Exception as e:
            data["news"] = []
            data["news_error"] = f"Could not fetch news: {str(e)}"

        try:
            # Analyst recommendations - suppress warnings
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                recommendations = ticker.recommendations
                if recommendations is not None and not recommendations.empty:
                    data["recommendations"] = recommendations.tail(5).to_dict('records')
        except Exception as e:
            data["recommendations"] = []
            data["recommendations_error"] = f"Could not fetch recommendations: {str(e)}"

        try:
            # Basic info
            info = ticker.info
            if info:
                data["company_info"] = {
                    "sector": info.get("sector", ""),
                    "industry": info.get("industry", ""),
                    "marketCap": info.get("marketCap", ""),
                    "peRatio": info.get("trailingPE", ""),
                    "dividendYield": info.get("dividendYield", "")
                }
        except:
            data["company_info"] = {}

        return data

    except Exception as e:
        return {"error": f"Failed to fetch comprehensive data for {symbol}: {str(e)}"}

def get_fii_dii_flows(period: str = "1mo", reason: str = "") -> Dict[str, Any]:
    """
    Get FII/DII flow data for Indian markets.
    This is crucial for Indian market analysis as institutional flows drive market direction.

    Args:
        period: Time period for flow data
        reason: Why this data is needed

    Returns:
        Dictionary with FII/DII flow information
    """
    try:
        # FII/DII data is typically available through NSE or specialized financial data providers
        # For now, we'll use proxy indicators and news-based analysis

        # Get Nifty 50 data as proxy for overall market sentiment
        nifty_data = fetch_market_data("^NSEI", period, "1d", reason=f"FII/DII proxy analysis: {reason}")

        # Get banking sector data as FII/DII flows heavily impact banking
        bank_nifty_data = fetch_market_data("^NSEBANK", period, "1d", reason="Banking sector FII/DII impact")

        # Calculate volume trends as proxy for institutional activity
        nifty_volume_trend = "increasing" if len(nifty_data.get("ohlcv", [])) > 5 else "stable"

        # Get news related to FII/DII flows
        fii_related_news = []
        try:
            # Search for FII/DII related news in major Indian stocks
            major_stocks = ["RELIANCE.NS", "TCS.NS", "HDFCBANK.NS"]
            for stock in major_stocks:
                ticker = yf.Ticker(stock)
                news = ticker.news
                if news:
                    for item in news[:2]:
                        title = item.get("title", "").lower()
                        if any(keyword in title for keyword in ["fii", "dii", "foreign", "institutional", "flows"]):
                            fii_related_news.append({
                                "title": item.get("title", ""),
                                "publisher": item.get("publisher", ""),
                                "link": item.get("link", ""),
                                "source_stock": stock
                            })
        except:
            pass

        return {
            "symbol": "FII_DII_FLOWS",
            "period": period,
            "reason": reason,
            "market_indicators": {
                "nifty_volume_trend": nifty_volume_trend,
                "nifty_price_change": nifty_data.get("price_data", {}).get("price_change_pct", 0),
                "banking_sector_performance": bank_nifty_data.get("price_data", {}).get("price_change_pct", 0)
            },
            "fii_dii_related_news": fii_related_news,
            "key_factors": [
                "Monitor FII/DII flow announcements from SEBI/NSE",
                "Track foreign portfolio investment (FPI) data",
                "Watch for mutual fund flow reports",
                "Check for changes in FII/DII holding patterns",
                "Monitor global risk sentiment affecting foreign flows"
            ],
            "data_sources": [
                "NSE official FII/DII data (manual check recommended)",
                "SEBI FPI reports",
                "Mutual fund association reports",
                "Financial news for flow-related announcements"
            ],
            "success": True
        }

    except Exception as e:
        return {
            "symbol": "FII_DII_FLOWS",
            "error": f"Failed to fetch FII/DII flow data: {str(e)}",
            "success": False
        }


def fetch_multiple_timeframes(
    symbol: str, 
    timeframes: list[dict[str, str]] = None,
    reason: str = ""
) -> Dict[str, Any]:
    """
    Fetch data for multiple timeframes for comprehensive analysis.
    
    Args:
        symbol: Ticker symbol
        timeframes: List of dictionaries with "period" and "interval" keys
        reason: Why multi-timeframe data is needed
        
    Returns:
        Dict containing data for all timeframes
    """
    if timeframes is None:
        timeframes = [
            {"period": "1d", "interval": "5m"},    # Short-term
            {"period": "5d", "interval": "15m"},   # Medium-term
            {"period": "1mo", "interval": "1d"}    # Long-term
        ]
    
    results = {
        "success": True,
        "symbol": symbol,
        "reason": reason,
        "timeframes": {}
    }
    
    for tf in timeframes:
        period = tf["period"]
        interval = tf["interval"]
        data = fetch_market_data(symbol, period, interval, f"Multi-timeframe analysis: {period}/{interval}")
        results["timeframes"][f"{period}_{interval}"] = data
        
        if not data["success"]:
            results["success"] = False
    
    return results


def get_market_context(symbols: list[str] = None, reason: str = "") -> Dict[str, Any]:
    """
    Get broader market context by fetching data for major indices.
    
    Args:
        symbols: List of symbols to fetch (defaults to major indices)
        reason: Why market context is needed
        
    Returns:
        Dict containing market context data
    """
    if symbols is None:
        symbols = ["^GSPC", "^IXIC", "^DJI", "^NSEI", "BTC-USD"]  # Major indices + BTC
    
    results = {
        "success": True,
        "reason": reason,
        "market_data": {}
    }
    
    for symbol in symbols:
        data = fetch_market_data(symbol, "1d", "1d", f"Market context for {symbol}")
        results["market_data"][symbol] = data
        
        if not data["success"]:
            results["success"] = False
    
    return results


def fetch_crypto_data(
    symbol: str,
    period: str = "30d",
    interval: str = "1h",
    reason: str = ""
) -> Dict[str, Any]:
    """
    Fetch crypto market data from Delta Exchange.

    This tool should be used for crypto symbols like BTCUSD, ETHUSD, SOLUSD.

    Args:
        symbol: Crypto symbol (e.g., 'BTCUSD', 'ETHUSD', 'SOLUSD')
        period: Time period ('1d', '7d', '30d', '90d')
        interval: Data interval ('1m', '5m', '15m', '1h', '4h', '1d')
        reason: Why this crypto data is needed for the analysis

    Returns:
        Dict containing the fetched crypto data and metadata
    """
    try:
        # Check if it's a crypto symbol
        if symbol.upper() not in MAIN_CRYPTO_SYMBOLS:
            return {
                "success": False,
                "symbol": symbol,
                "error": f"Crypto symbol {symbol} not supported. Available: {MAIN_CRYPTO_SYMBOLS}",
                "reason": reason
            }

        # Fetch from Delta Exchange
        data = get_crypto_market_data(symbol.upper(), period, interval, reason)

        if "error" in data:
            return {
                "success": False,
                "symbol": symbol,
                "error": data["error"],
                "reason": reason
            }

        return {
            "success": True,
            "symbol": symbol,
            "data": data,
            "source": "Delta Exchange",
            "period": period,
            "interval": interval,
            "reason": reason,
            "current_price": data.get("current_price"),
            "price_change_24h": data.get("price_change_24h"),
            "summary": data.get("summary", {})
        }

    except Exception as e:
        return {
            "success": False,
            "symbol": symbol,
            "error": f"Error fetching crypto data: {str(e)}",
            "reason": reason
        }


def fetch_multiple_crypto_timeframes(
    symbol: str,
    timeframes: Optional[list] = None,
    reason: str = ""
) -> Dict[str, Any]:
    """
    Fetch crypto data for multiple timeframes for comprehensive analysis.

    Args:
        symbol: Crypto symbol (e.g., 'BTCUSD', 'ETHUSD', 'SOLUSD')
        timeframes: List of timeframe dicts with 'period' and 'interval'
        reason: Why multi-timeframe crypto data is needed

    Returns:
        Dict containing multi-timeframe crypto data
    """
    if timeframes is None:
        timeframes = [
            {"period": "1d", "interval": "15m"},   # Short-term scalping
            {"period": "7d", "interval": "1h"},    # Intraday analysis
            {"period": "30d", "interval": "4h"},   # Swing trading
            {"period": "90d", "interval": "1d"}    # Long-term trend
        ]

    results = {
        "success": True,
        "symbol": symbol,
        "source": "Delta Exchange",
        "reason": reason,
        "timeframes": {}
    }

    for tf in timeframes:
        period = tf.get("period", "7d")
        interval = tf.get("interval", "1h")
        tf_key = f"{period}_{interval}"

        data = fetch_crypto_data(symbol, period, interval, f"Multi-timeframe analysis: {tf_key}")
        results["timeframes"][tf_key] = data

        if not data["success"]:
            results["success"] = False

    return results


def get_crypto_market_context(
    symbols: Optional[list] = None,
    reason: str = ""
) -> Dict[str, Any]:
    """
    Get broader crypto market context by fetching data for main crypto symbols.

    Args:
        symbols: List of crypto symbols (defaults to main trading symbols)
        reason: Why crypto market context is needed

    Returns:
        Dict containing crypto market context data
    """
    if symbols is None:
        symbols = MAIN_CRYPTO_SYMBOLS

    results = {
        "success": True,
        "source": "Delta Exchange",
        "reason": reason,
        "market_data": {}
    }

    for symbol in symbols:
        data = fetch_crypto_data(symbol, "7d", "1h", f"Crypto market context for {symbol}")
        results["market_data"][symbol] = data

        if not data["success"]:
            results["success"] = False

    return results
