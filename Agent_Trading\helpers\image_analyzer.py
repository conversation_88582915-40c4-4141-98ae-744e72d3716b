"""
Advanced Image Analysis for Trading Charts
Object detection and image segmentation for better chart understanding
"""

import io
try:
    import cv2
    CV2_AVAILABLE = True
    VISION_BACKEND = 'opencv'
except ImportError:
    CV2_AVAILABLE = False
    try:
        # Try scikit-image as cv2 alternative (Python 3.13 compatible)
        from skimage import feature, measure, segmentation, color, filters, morphology  # type: ignore
        from skimage.feature import canny  # type: ignore
        from skimage.measure import find_contours, approximate_polygon  # type: ignore
        from skimage.color import rgb2gray, rgb2hsv  # type: ignore
        from skimage.filters import gaussian  # type: ignore
        import scipy.ndimage as ndimage  # type: ignore
        SCIKIT_AVAILABLE = True
        VISION_BACKEND = 'scikit'
        print("✅ Using scikit-image for computer vision (Python 3.13 compatible)")
    except ImportError:
        SCIKIT_AVAILABLE = False
        VISION_BACKEND = 'none'
        print("⚠️ No computer vision library available. Using basic PIL analysis only.")

import numpy as np
from PIL import Image, ImageDraw, ImageFont
from typing import Dict, Any, List, Tuple, Optional
import logging
import re

logger = logging.getLogger(__name__)

class AdvancedImageAnalyzer:
    """Advanced image analysis using computer vision techniques"""
    
    def __init__(self):
        self.chart_elements = {
            'candlesticks': {'color_range': [(0, 100, 0), (80, 255, 80)]},  # Green range
            'volume_bars': {'position': 'bottom_third'},
            'trend_lines': {'min_length': 50},
            'price_levels': {'horizontal_threshold': 0.95}
        }
    
    def analyze_chart_structure(self, image_data: bytes) -> Dict[str, Any]:
        """
        Analyze chart structure using computer vision
        
        Returns:
            Dict containing detected chart elements and metadata
        """
        try:
            # Load image
            image = Image.open(io.BytesIO(image_data))

            if VISION_BACKEND == 'none':
                # Fallback analysis without computer vision
                return self._analyze_chart_structure_fallback(image)
            elif VISION_BACKEND == 'scikit':
                # Use scikit-image backend
                return self._analyze_chart_structure_scikit(image)
            elif VISION_BACKEND == 'opencv':
                # Convert to OpenCV format
                cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            else:
                # Fallback
                return self._analyze_chart_structure_fallback(image)
            
            analysis_result = {
                'chart_type': self._detect_chart_type(cv_image),
                'timeframe': self._detect_timeframe(cv_image, image),
                'price_scale': self._detect_price_scale(cv_image, image),
                'volume_present': self._detect_volume_section(cv_image),
                'indicators': self._detect_indicators(cv_image),
                'trend_lines': self._detect_trend_lines(cv_image),
                'support_resistance': self._detect_support_resistance(cv_image),
                'chart_dimensions': {
                    'width': cv_image.shape[1],
                    'height': cv_image.shape[0]
                },
                'analysis_confidence': 0.0
            }
            
            # Calculate overall confidence
            analysis_result['analysis_confidence'] = self._calculate_confidence(analysis_result)
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"Chart structure analysis failed: {e}")
            return {
                'error': str(e),
                'chart_type': 'unknown',
                'analysis_confidence': 0.0
            }

    def _analyze_chart_structure_fallback(self, image: Image.Image) -> Dict[str, Any]:
        """Fallback analysis without OpenCV - basic image analysis"""
        try:
            width, height = image.size

            # Basic analysis using PIL only
            analysis_result = {
                'chart_type': 'candlestick',  # Default assumption
                'timeframe': 'unknown',
                'price_scale': 'linear',
                'volume_present': height > 400,  # Assume volume if tall enough
                'indicators': ['price_action'],  # Basic assumption
                'analysis_confidence': 0.5,  # Lower confidence without CV
                'image_dimensions': {'width': width, 'height': height},
                'fallback_mode': True
            }

            # Try to detect some basic features
            # Convert to grayscale for basic analysis
            grayscale = image.convert('L')
            pixels = np.array(grayscale)

            # Basic brightness analysis
            avg_brightness = np.mean(pixels)
            analysis_result['brightness'] = 'dark' if avg_brightness < 128 else 'light'

            # Basic complexity analysis (edge detection approximation)
            # Simple gradient calculation
            grad_x = np.abs(np.diff(pixels, axis=1))
            grad_y = np.abs(np.diff(pixels, axis=0))
            edge_density = (np.mean(grad_x) + np.mean(grad_y)) / 2

            if edge_density > 20:
                analysis_result['complexity'] = 'high'
                analysis_result['indicators'].append('technical_indicators')
            elif edge_density > 10:
                analysis_result['complexity'] = 'medium'
            else:
                analysis_result['complexity'] = 'low'

            return analysis_result

        except Exception as e:
            logger.error(f"Fallback chart analysis failed: {e}")
            return {
                'chart_type': 'unknown',
                'timeframe': 'unknown',
                'price_scale': 'unknown',
                'volume_present': False,
                'indicators': [],
                'analysis_confidence': 0.0,
                'fallback_mode': True,
                'error': str(e)
            }

    def _analyze_chart_structure_scikit(self, image: Image.Image) -> Dict[str, Any]:
        """Advanced analysis using scikit-image (Python 3.13 compatible)"""
        try:
            if not SCIKIT_AVAILABLE:
                return self._analyze_chart_structure_fallback(image)

            # Convert PIL image to numpy array
            img_array = np.array(image)
            width, height = image.size

            # Convert to grayscale for analysis
            gray = rgb2gray(img_array)

            # Edge detection using Canny
            edges = canny(gray, sigma=1.0, low_threshold=0.1, high_threshold=0.2)

            # Find contours
            contours = find_contours(edges, 0.5)

            # Analyze chart structure
            analysis_result = {
                'chart_type': self._detect_chart_type_scikit(img_array, edges, contours),
                'timeframe': 'unknown',  # Would need OCR for accurate detection
                'price_scale': 'linear',  # Default assumption
                'volume_present': height > 400,  # Assume volume if tall enough
                'indicators': self._detect_indicators_scikit(img_array),
                'analysis_confidence': 0.8,  # Higher confidence with scikit
                'image_dimensions': {'width': width, 'height': height},
                'scikit_mode': True,
                'edge_density': np.sum(edges) / edges.size,
                'contour_count': len(contours)
            }

            return analysis_result

        except Exception as e:
            logger.error(f"Scikit-image chart analysis failed: {e}")
            return self._analyze_chart_structure_fallback(image)

    def _detect_chart_type_scikit(self, img_array: np.ndarray, edges: np.ndarray, contours: list) -> str:
        """Detect chart type using scikit-image"""
        try:
            # Count rectangular shapes (potential candlesticks)
            rectangular_shapes = 0

            for contour in contours:
                if len(contour) > 10:  # Minimum points for a meaningful contour
                    # Approximate contour to polygon
                    epsilon = 0.02 * len(contour)
                    approx = approximate_polygon(contour, tolerance=epsilon)

                    # Check if it's roughly rectangular (4 corners)
                    if len(approx) == 4:
                        # Calculate bounding box
                        min_row, min_col = np.min(contour, axis=0)
                        max_row, max_col = np.max(contour, axis=0)
                        width = max_col - min_col
                        height = max_row - min_row

                        # Check if dimensions are typical for candlesticks
                        if 5 < width < 50 and 10 < height < 200:
                            rectangular_shapes += 1

            # Determine chart type based on rectangular shapes
            if rectangular_shapes > 10:
                return 'candlestick'
            elif rectangular_shapes > 5:
                return 'bar'
            else:
                return 'line'

        except Exception as e:
            logger.error(f"Chart type detection failed: {e}")
            return 'candlestick'  # Default assumption

    def _detect_indicators_scikit(self, img_array: np.ndarray) -> List[str]:
        """Detect technical indicators using scikit-image"""
        try:
            indicators_found = ['price_action']  # Always present

            # Convert to HSV for better color detection
            hsv = rgb2hsv(img_array)

            # Define color ranges for common indicators (in HSV)
            indicator_colors = {
                'moving_average': [(0.0, 0.0, 0.8), (1.0, 0.3, 1.0)],  # Light colors
                'bollinger_bands': [(0.5, 0.3, 0.5), (0.7, 0.8, 0.9)],  # Blue-ish
                'rsi': [(0.8, 0.5, 0.5), (1.0, 1.0, 1.0)],  # Red-ish
                'macd': [(0.3, 0.4, 0.4), (0.4, 0.8, 0.8)]   # Green-ish
            }

            height, width = img_array.shape[:2]
            total_pixels = height * width

            for indicator_name, (lower, upper) in indicator_colors.items():
                # Create mask for color range
                h_mask = (hsv[:,:,0] >= lower[0]) & (hsv[:,:,0] <= upper[0])
                s_mask = (hsv[:,:,1] >= lower[1]) & (hsv[:,:,1] <= upper[1])
                v_mask = (hsv[:,:,2] >= lower[2]) & (hsv[:,:,2] <= upper[2])

                mask = h_mask & s_mask & v_mask
                color_pixels = np.sum(mask)

                if color_pixels / total_pixels > 0.001:  # At least 0.1% of pixels
                    indicators_found.append(indicator_name)

            return indicators_found

        except Exception as e:
            logger.error(f"Indicator detection failed: {e}")
            return ['price_action']

    def _detect_chart_type(self, cv_image: np.ndarray) -> str:
        """Detect if chart is candlestick, line, or bar chart"""
        
        # Convert to HSV for better color detection
        hsv = cv2.cvtColor(cv_image, cv2.COLOR_BGR2HSV)
        
        # Look for candlestick patterns (rectangular shapes)
        gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, 50, 150)
        
        # Find contours
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Count rectangular shapes (potential candlesticks)
        rectangular_shapes = 0
        for contour in contours:
            # Approximate contour
            epsilon = 0.02 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)
            
            # Check if it's roughly rectangular and appropriate size
            if len(approx) == 4:
                x, y, w, h = cv2.boundingRect(contour)
                if 5 < w < 50 and 10 < h < 200:  # Typical candlestick dimensions
                    rectangular_shapes += 1
        
        # Determine chart type based on rectangular shapes found
        if rectangular_shapes > 20:
            return 'candlestick'
        elif rectangular_shapes > 5:
            return 'bar'
        else:
            return 'line'
    
    def _detect_timeframe(self, cv_image: np.ndarray, pil_image: Image.Image) -> str:
        """Detect timeframe from chart labels"""
        
        # Common timeframe patterns to look for
        timeframe_patterns = [
            r'1[mM]', r'5[mM]', r'15[mM]', r'30[mM]',
            r'1[hH]', r'4[hH]', r'1[dD]', r'1[wW]'
        ]
        
        # Try to extract text from image (simplified OCR simulation)
        # In a real implementation, you'd use pytesseract or similar
        
        # For now, analyze image characteristics to guess timeframe
        width = cv_image.shape[1]
        
        # More data points usually indicate shorter timeframes
        gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, 50, 150)
        
        # Count vertical lines (potential price bars)
        vertical_lines = 0
        for x in range(0, width, 10):  # Sample every 10 pixels
            column = edges[:, x]
            if np.sum(column > 0) > 50:  # Significant vertical activity
                vertical_lines += 1
        
        # Estimate timeframe based on data density
        data_density = vertical_lines / (width / 10)
        
        if data_density > 50:
            return '1m'
        elif data_density > 30:
            return '5m'
        elif data_density > 15:
            return '15m'
        elif data_density > 8:
            return '1h'
        elif data_density > 4:
            return '4h'
        else:
            return '1d'
    
    def _detect_price_scale(self, cv_image: np.ndarray, pil_image: Image.Image) -> Dict[str, Any]:
        """Detect price scale and current price levels"""
        
        height, width = cv_image.shape[:2]
        
        # Assume price scale is on the right side (last 15% of width)
        price_area_start = int(width * 0.85)
        price_area = cv_image[:, price_area_start:]
        
        # Look for horizontal text (price labels)
        gray_price_area = cv2.cvtColor(price_area, cv2.COLOR_BGR2GRAY)
        
        # Find horizontal lines that might indicate price levels
        horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (40, 1))
        horizontal_lines = cv2.morphologyEx(gray_price_area, cv2.MORPH_OPEN, horizontal_kernel)
        
        # Find contours of horizontal lines
        contours, _ = cv2.findContours(horizontal_lines, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        price_levels = []
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            if w > 20 and h < 5:  # Horizontal line characteristics
                # Convert back to full image coordinates
                full_y = y
                price_levels.append({
                    'y_position': full_y,
                    'relative_position': full_y / height
                })
        
        return {
            'detected_levels': len(price_levels),
            'price_levels': price_levels[:10],  # Limit to top 10
            'scale_area': {
                'start_x': price_area_start,
                'width': width - price_area_start
            }
        }
    
    def _detect_volume_section(self, cv_image: np.ndarray) -> bool:
        """Detect if chart has volume bars"""
        
        height, width = cv_image.shape[:2]
        
        # Volume is typically in bottom 20-30% of chart
        volume_area_start = int(height * 0.7)
        volume_area = cv_image[volume_area_start:, :]
        
        # Look for vertical bars (volume bars)
        gray_volume = cv2.cvtColor(volume_area, cv2.COLOR_BGR2GRAY)
        
        # Find vertical lines
        vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 10))
        vertical_lines = cv2.morphologyEx(gray_volume, cv2.MORPH_OPEN, vertical_kernel)
        
        # Count vertical structures
        contours, _ = cv2.findContours(vertical_lines, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        vertical_bars = 0
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            if h > 10 and w < 20:  # Vertical bar characteristics
                vertical_bars += 1
        
        return vertical_bars > 10  # Threshold for volume presence
    
    def _detect_indicators(self, cv_image: np.ndarray) -> List[str]:
        """Detect technical indicators on chart"""
        
        indicators_found = []
        
        # Convert to HSV for color-based detection
        hsv = cv2.cvtColor(cv_image, cv2.COLOR_BGR2HSV)
        
        # Look for common indicator colors
        indicator_colors = {
            'moving_average': [(100, 50, 50), (130, 255, 255)],  # Blue range
            'rsi': [(0, 50, 50), (20, 255, 255)],                # Red range
            'macd': [(40, 50, 50), (80, 255, 255)]               # Green range
        }
        
        for indicator_name, (lower, upper) in indicator_colors.items():
            # Create mask for color range
            mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
            
            # Check if significant pixels match this color
            color_pixels = np.sum(mask > 0)
            total_pixels = cv_image.shape[0] * cv_image.shape[1]
            
            if color_pixels / total_pixels > 0.001:  # At least 0.1% of pixels
                indicators_found.append(indicator_name)
        
        return indicators_found
    
    def _detect_trend_lines(self, cv_image: np.ndarray) -> List[Dict[str, Any]]:
        """Detect trend lines using Hough line transform"""
        
        gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, 50, 150)
        
        # Use Hough line transform to detect lines
        lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50, 
                               minLineLength=100, maxLineGap=10)
        
        trend_lines = []
        if lines is not None:
            for line in lines:
                x1, y1, x2, y2 = line[0]
                
                # Calculate line properties
                length = np.sqrt((x2-x1)**2 + (y2-y1)**2)
                angle = np.arctan2(y2-y1, x2-x1) * 180 / np.pi
                
                # Filter for trend lines (not too vertical or horizontal)
                if 10 < abs(angle) < 80 and length > 100:
                    trend_lines.append({
                        'start': (x1, y1),
                        'end': (x2, y2),
                        'length': length,
                        'angle': angle,
                        'type': 'uptrend' if angle > 0 else 'downtrend'
                    })
        
        return trend_lines[:5]  # Return top 5 trend lines
    
    def _detect_support_resistance(self, cv_image: np.ndarray) -> Dict[str, List]:
        """Detect horizontal support and resistance levels"""
        
        gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, 50, 150)
        
        # Detect horizontal lines
        horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (40, 1))
        horizontal_lines = cv2.morphologyEx(edges, cv2.MORPH_OPEN, horizontal_kernel)
        
        # Find contours
        contours, _ = cv2.findContours(horizontal_lines, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        height = cv_image.shape[0]
        support_levels = []
        resistance_levels = []
        
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            
            # Filter for significant horizontal lines
            if w > 100 and h < 5:
                relative_y = y / height
                
                # Classify as support (lower half) or resistance (upper half)
                if relative_y > 0.5:
                    support_levels.append({
                        'y_position': y,
                        'relative_position': relative_y,
                        'strength': w  # Line length as strength indicator
                    })
                else:
                    resistance_levels.append({
                        'y_position': y,
                        'relative_position': relative_y,
                        'strength': w
                    })
        
        # Sort by strength (line length)
        support_levels.sort(key=lambda x: x['strength'], reverse=True)
        resistance_levels.sort(key=lambda x: x['strength'], reverse=True)
        
        return {
            'support': support_levels[:3],      # Top 3 support levels
            'resistance': resistance_levels[:3] # Top 3 resistance levels
        }
    
    def _calculate_confidence(self, analysis_result: Dict[str, Any]) -> float:
        """Calculate overall analysis confidence"""
        
        confidence_factors = []
        
        # Chart type detection confidence
        if analysis_result['chart_type'] != 'unknown':
            confidence_factors.append(0.8)
        
        # Price scale detection
        if analysis_result['price_scale']['detected_levels'] > 0:
            confidence_factors.append(0.7)
        
        # Volume detection
        if analysis_result['volume_present']:
            confidence_factors.append(0.6)
        
        # Indicators detection
        if analysis_result['indicators']:
            confidence_factors.append(0.5)
        
        # Trend lines detection
        if analysis_result['trend_lines']:
            confidence_factors.append(0.4)
        
        # Support/resistance detection
        sr_levels = analysis_result['support_resistance']
        if sr_levels['support'] or sr_levels['resistance']:
            confidence_factors.append(0.3)
        
        # Calculate weighted average
        if confidence_factors:
            return min(0.95, sum(confidence_factors) / len(confidence_factors))
        else:
            return 0.1
    
    def create_analysis_overlay(self, image_data: bytes, analysis_result: Dict[str, Any]) -> bytes:
        """Create overlay showing detected chart elements"""
        
        try:
            image = Image.open(io.BytesIO(image_data))
            draw = ImageDraw.Draw(image)
            
            # Draw trend lines
            for trend_line in analysis_result.get('trend_lines', []):
                start = trend_line['start']
                end = trend_line['end']
                color = '#00FF00' if trend_line['type'] == 'uptrend' else '#FF0000'
                draw.line([start, end], fill=color, width=2)
            
            # Draw support/resistance levels
            sr_levels = analysis_result.get('support_resistance', {})
            width = image.width
            
            for support in sr_levels.get('support', []):
                y = support['y_position']
                draw.line([(0, y), (width, y)], fill='#FFD700', width=1)  # Gold for support
            
            for resistance in sr_levels.get('resistance', []):
                y = resistance['y_position']
                draw.line([(0, y), (width, y)], fill='#FF6B6B', width=1)  # Red for resistance
            
            # Convert back to bytes
            output_buffer = io.BytesIO()
            image.save(output_buffer, format='JPEG', quality=95)
            return output_buffer.getvalue()
            
        except Exception as e:
            logger.error(f"Failed to create analysis overlay: {e}")
            return image_data

# Global instance
advanced_image_analyzer = AdvancedImageAnalyzer()
