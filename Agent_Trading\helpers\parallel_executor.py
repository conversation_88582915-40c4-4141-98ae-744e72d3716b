"""
Parallel Tool Execution for Fast Trading Analysis
Reduces analysis time from 184s to ~30-45s by running tools concurrently
"""

import asyncio
import concurrent.futures
import logging
import time
from typing import Dict, List, Any, Callable, Optional
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class ToolResult:
    """Result from a tool execution"""
    tool_name: str
    success: bool
    data: Any
    error: Optional[str] = None
    execution_time: float = 0.0
    timestamp: str = ""

class ParallelToolExecutor:
    """Execute trading analysis tools in parallel for better performance"""
    
    def __init__(self, max_workers: int = 4, timeout: int = 30):
        self.max_workers = max_workers
        self.timeout = timeout
        
    def execute_tools_parallel(self, tool_configs: List[Dict[str, Any]], progress_callback=None) -> Dict[str, ToolResult]:
        """
        Execute multiple tools in parallel
        
        Args:
            tool_configs: List of tool configurations with 'name', 'function', and 'args'
            progress_callback: Optional callback for progress updates
            
        Returns:
            Dictionary mapping tool names to their results
        """
        start_time = time.time()
        results = {}
        
        if progress_callback:
            progress_callback("🚀 Starting parallel tool execution...", 1, len(tool_configs) + 2)
        
        try:
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # Submit all tools for execution
                future_to_tool = {}
                
                for config in tool_configs:
                    tool_name = config['name']
                    tool_function = config['function']
                    tool_args = config.get('args', {})
                    
                    future = executor.submit(self._execute_single_tool, tool_name, tool_function, tool_args)
                    future_to_tool[future] = tool_name
                
                if progress_callback:
                    progress_callback(f"⚡ Executing {len(tool_configs)} tools in parallel...", 2, len(tool_configs) + 2)
                
                # Collect results as they complete
                completed_count = 0
                for future in concurrent.futures.as_completed(future_to_tool, timeout=self.timeout):
                    tool_name = future_to_tool[future]
                    completed_count += 1
                    
                    try:
                        result = future.result()
                        results[tool_name] = result
                        
                        if progress_callback:
                            progress_callback(
                                f"✅ {tool_name} completed ({completed_count}/{len(tool_configs)})",
                                2 + completed_count,
                                len(tool_configs) + 2
                            )
                        
                    except Exception as e:
                        logger.error(f"Tool {tool_name} failed: {str(e)}")
                        results[tool_name] = ToolResult(
                            tool_name=tool_name,
                            success=False,
                            data=None,
                            error=str(e),
                            execution_time=0.0,
                            timestamp=datetime.now().isoformat()
                        )
                        
                        if progress_callback:
                            progress_callback(
                                f"❌ {tool_name} failed ({completed_count}/{len(tool_configs)})",
                                2 + completed_count,
                                len(tool_configs) + 2
                            )
        
        except concurrent.futures.TimeoutError:
            logger.error(f"Parallel execution timed out after {self.timeout}s")
            if progress_callback:
                progress_callback("⏰ Some tools timed out", len(tool_configs) + 1, len(tool_configs) + 2)
        
        total_time = time.time() - start_time
        
        if progress_callback:
            progress_callback(f"🎯 Parallel execution completed in {total_time:.1f}s", len(tool_configs) + 2, len(tool_configs) + 2)
        
        logger.info(f"Parallel execution completed in {total_time:.1f}s with {len(results)} results")
        return results
    
    def _execute_single_tool(self, tool_name: str, tool_function: Callable, tool_args: Dict[str, Any]) -> ToolResult:
        """Execute a single tool and return result"""
        start_time = time.time()
        
        try:
            logger.info(f"Executing tool: {tool_name}")
            result_data = tool_function(**tool_args)
            execution_time = time.time() - start_time
            
            return ToolResult(
                tool_name=tool_name,
                success=True,
                data=result_data,
                execution_time=execution_time,
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Tool {tool_name} failed after {execution_time:.1f}s: {str(e)}")
            
            return ToolResult(
                tool_name=tool_name,
                success=False,
                data=None,
                error=str(e),
                execution_time=execution_time,
                timestamp=datetime.now().isoformat()
            )

class TradingAnalysisExecutor:
    """Specialized executor for trading analysis workflows"""
    
    def __init__(self):
        self.executor = ParallelToolExecutor(max_workers=4, timeout=45)
    
    def execute_indian_market_analysis(self, symbol: str, progress_callback=None) -> Dict[str, ToolResult]:
        """Execute Indian market analysis tools in parallel"""
        
        # Import tools here to avoid circular imports
        from .duckduckgo_news import duckduckgo_news
        from .data_tools import fetch_market_data
        from .indian_market_tools import indian_market_analyzer
        
        tool_configs = [
            {
                'name': 'detect_trading_symbol',
                'function': indian_market_analyzer.detect_symbol_from_text,
                'args': {'text_input': symbol}
            },
            {
                'name': 'get_market_news',
                'function': duckduckgo_news.get_market_news,
                'args': {'symbol': symbol, 'max_results': 10}
            },
            {
                'name': 'get_fii_dii_news',
                'function': duckduckgo_news.get_fii_dii_news,
                'args': {}
            },
            {
                'name': 'get_market_data',
                'function': fetch_market_data,
                'args': {'symbol': symbol, 'period': '5d', 'interval': '1d'}
            }
        ]
        
        return self.executor.execute_tools_parallel(tool_configs, progress_callback)
    
    def execute_crypto_analysis(self, symbol: str, progress_callback=None) -> Dict[str, ToolResult]:
        """Execute crypto market analysis tools in parallel"""
        
        from .duckduckgo_news import duckduckgo_news
        from .data_tools import fetch_crypto_data
        
        tool_configs = [
            {
                'name': 'get_crypto_news',
                'function': duckduckgo_news.get_market_news,
                'args': {'symbol': symbol, 'max_results': 8}
            },
            {
                'name': 'get_crypto_data',
                'function': fetch_crypto_data,
                'args': {'symbol': symbol, 'period': '5d', 'interval': '1h'}
            },
            {
                'name': 'get_comprehensive_crypto_news',
                'function': duckduckgo_news.get_comprehensive_market_news,
                'args': {'symbol': symbol, 'max_results': 12}
            }
        ]
        
        return self.executor.execute_tools_parallel(tool_configs, progress_callback)
    
    def execute_general_analysis(self, symbol: str, progress_callback=None) -> Dict[str, ToolResult]:
        """Execute general market analysis tools in parallel"""
        
        from .duckduckgo_news import duckduckgo_news
        from .data_tools import fetch_market_data
        
        tool_configs = [
            {
                'name': 'get_general_news',
                'function': duckduckgo_news.get_market_news,
                'args': {'symbol': symbol, 'max_results': 10}
            },
            {
                'name': 'get_market_data',
                'function': fetch_market_data,
                'args': {'symbol': symbol, 'period': '5d', 'interval': '1d'}
            }
        ]
        
        return self.executor.execute_tools_parallel(tool_configs, progress_callback)

# Global instance
trading_executor = TradingAnalysisExecutor()
